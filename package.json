{"name": "liberom-front", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite dev --host", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --check . && eslint .", "format": "prettier --write ."}, "dependencies": {"flowbite": "^2.1.1", "svelte-select": "^5.8.1", "sveltekit-i18n": "^2.4.2"}, "devDependencies": {"@internationalized/date": "^3.8.2", "@sveltejs/adapter-cloudflare": "^2.3.4", "@sveltejs/adapter-static": "^2.0.3", "@sveltejs/kit": "^1.27.4", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "autoprefixer": "^10.4.16", "bits-ui": "^0.22.0", "clsx": "^2.1.1", "eslint": "^8.28.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-svelte": "^2.30.0", "lucide-svelte": "^0.488.0", "prettier": "^3.1.1", "prettier-plugin-svelte": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.9", "svelte": "^4.0.5", "svelte-check": "^3.6.0", "tailwind-merge": "^3.2.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^3.3.5", "tslib": "^2.4.1", "typescript": "^5.0.0", "vite": "^4.4.2", "vite-plugin-mkcert": "^1.17.5"}}