APP_NAME=Liberom
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://127.0.0.1:8000
APP_ADMIN_EMAIL=<EMAIL>

FRONT_URL=https://localhost.liberom.com
FRONT_VERIFY_EMAIL_URL=/register
FRONT_LOGIN_URL=/reset-password

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug
LOG_SLACK_WEBHOOK_URL=
LOG_SLACK_LEVEL=warning
TELESCOPE_ENABLED=true

DB_TYPE=mysql
DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=liberom
DB_DATABASE_IMPORT=
DB_USERNAME=sail
DB_PASSWORD=password

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
# Remember to use "sail artisan queue:listen" to start the queue on local
# QUEUE_CONNECTION=redis
SESSION_DRIVER=file
SESSION_LIFETIME=120

REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

SCOUT_DRIVER=meilisearch
SCOUT_QUEUE=true
MEILISEARCH_HOST=http://meilisearch:7700
MEILISEARCH_KEY=

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

ISBNDB_API_KEY=
OPENAI_API_KEY=
DEEPL_API_KEY=
AZURE_TRANSLATION_API_KEY=
AZURE_REGION=
GOOGLE_APPLICATION_CREDENTIALS=
GOOGLE_PROJECT_ID=

NOVA_LICENSE_KEY=

LOCATION_TESTING=false

TRANSLATION_SERVICE=google_free

SAIL_APP_PORT=443
SAIL_API_PORT=8000
SAIL_MYSQL_PORT=3306
SAIL_REDIS_PORT=6379
SAIL_MEILISEARCH_PORT=7700

# Set to off to disable xdebug
SAIL_XDEBUG_MODE=develop,debug,coverage
# For Linux see: https://laravel.com/docs/10.x/sail#debugging-with-xdebug
SAIL_XDEBUG_CONFIG="client_host=host.docker.internal log_level=0"
# serverName is the name from PHPStorm Settings->PHP->Servers. Server host: 0.0.0.0; server port: 80.
SAIL_XDEBUG_PHP_IDE_CONFIG="serverName=Liberom"

PYTHON_PATH=
TTS_SCRIPT_PATH=
