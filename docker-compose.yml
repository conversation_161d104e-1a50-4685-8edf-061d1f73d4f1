services:
    laravel.test:
        build:
            context: ./vendor/laravel/sail/runtimes/8.2
            dockerfile: Dockerfile
            args:
                WWWGROUP: '${WWWGROUP}'
        image: sail-8.2/app
        extra_hosts:
            - 'host.docker.internal:host-gateway'
        ports:
            - '${SAIL_API_PORT:-80}:80'
        environment:
            WWWUSER: '${WWWUSER}' # The bootstrap and storage folder require 777 permissions
            LARAVEL_SAIL: 1
            XDEBUG_MODE: '${SAIL_XDEBUG_MODE:-off}'
            XDEBUG_CONFIG: '${SAIL_XDEBUG_CONFIG:-client_host=host.docker.internal}'
            XDEBUG_SESSION: 1
            PHP_IDE_CONFIG: '${SAIL_XDEBUG_PHP_IDE_CONFIG}'
        volumes:
            - '.:/var/www/html'
        networks:
            - sail
        depends_on:
            - redis
            - meilisearch
            - mysql
    vue:
        image: 'node:current-alpine'
        ports:
            - '444:443'
        environment:
            NODE_ENV: development
        volumes:
            - './front:/var/www/html'
        networks:
            - sail
        working_dir: /var/www/html
        user: root
        command: 'npm run serve'
        depends_on:
            - laravel.test
    front:
        image: 'node:current-alpine'
        ports:
            - '443:5173'
        environment:
            NODE_ENV: development
        volumes:
            - '../liberom-front:/var/www/html'
        networks:
            - sail
        working_dir: /var/www/html
        user: root
        command: 'sh -c "npm install && npm run dev -- --https"'
        depends_on:
            - laravel.test
    redis:
        image: 'redis:alpine'
        ports:
            - '${SAIL_REDIS_PORT:-6379}:6379'
        volumes:
            - 'sail-redis:/data'
        networks:
            - sail
        healthcheck:
            test:
                - CMD
                - redis-cli
                - ping
            retries: 3
            timeout: 5s
    meilisearch:
        image: 'getmeili/meilisearch:v1.14.0'
        ports:
            - '${SAIL_MEILISEARCH_PORT:-7700}:7700'
        volumes:
            - 'sail-meilisearch:/meili_data'
        networks:
            - sail
        healthcheck:
            test:
                - CMD
                - wget
                - '--no-verbose'
                - '--spider'
                - 'http://localhost:7700/health'
            retries: 3
            timeout: 5s
    mysql:
        image: 'mysql/mysql-server:8.0'
        ports:
            - '${FORWARD_DB_PORT:-3306}:3306'
        command: --default-authentication-plugin=mysql_native_password # Sequel Pro SQL client was not working with the newer authentication plugin
        environment:
            MYSQL_ROOT_PASSWORD: '${DB_PASSWORD}'
            MYSQL_ROOT_HOST: '%'
            MYSQL_DATABASE: '${DB_DATABASE}'
            MYSQL_USER: '${DB_USERNAME}'
            MYSQL_PASSWORD: '${DB_PASSWORD}'
            MYSQL_ALLOW_EMPTY_PASSWORD: 1
        volumes:
            - 'sail-mysql:/var/lib/mysql'
            - './vendor/laravel/sail/database/mysql/create-testing-database.sh:/docker-entrypoint-initdb.d/10-create-testing-database.sh'
        networks:
            - sail
        healthcheck:
            test:
                - CMD
                - mysqladmin
                - ping
                - '-p${DB_PASSWORD}'
            retries: 3
            timeout: 5s
networks:
    sail:
        driver: bridge
volumes:
    sail-redis:
        driver: local
    sail-meilisearch:
        driver: local
    sail-mysql:
        driver: local
