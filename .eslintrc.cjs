module.exports = {
    root: true,
    extends: [
        'eslint:recommended',
        'plugin:@typescript-eslint/recommended',
        'plugin:svelte/recommended',
        'prettier',
    ],
    parser: '@typescript-eslint/parser',
    plugins: [
        '@typescript-eslint',
        'simple-import-sort',
    ],
    parserOptions: {
        sourceType: 'module',
        ecmaVersion: 2022,
        extraFileExtensions: ['.svelte'],
        project: ['tsconfig.json'],
    },
    env: {
        browser: true,
        es2022: true,
    },
    overrides: [
        {
            files: ['*.svelte'],
            parser: 'svelte-eslint-parser',
            parserOptions: {
                parser: '@typescript-eslint/parser',
            },
        },
    ],
    rules: {
        /*
         * JavaScript rules
         */
        'array-callback-return': ['error', {checkForEach: true, allowImplicit: false, allowVoid: false}],
        'no-await-in-loop': ['error'],
        'no-constant-binary-expression': ['error'],
        'no-constructor-return': ['error'],
        'no-duplicate-imports': ['error', {includeExports: true}],
        'no-new-native-nonconstructor': ['error'],
        'no-promise-executor-return': ['error'],
        'no-self-compare': ['error'],
        'no-template-curly-in-string': ['error'],
        'no-unmodified-loop-condition': ['error'],
        'no-unreachable-loop': ['error'],
        'no-unused-private-class-members': ['error'],
        'require-atomic-updates': ['off'],
        'block-scoped-var': ['error'],
        'camelcase': ['error', {ignoreImports: true}],
        'complexity': ['error', {max: 20}],
        'consistent-return': ['error'],
        'curly': ['error'],
        'default-case-last': ['error'],
        'eqeqeq': ['error', 'always'],
        'func-names': ['error', 'never', {generators: 'never'}],
        'guard-for-in': ['error'],
        'id-denylist': ['error', 'err', 'e', 'cb'],
        'id-length': ['off'],
        'logical-assignment-operators': ['error', 'never'],
        'max-classes-per-file': ['error', 1],
        'max-depth': ['error', 3],
        'max-lines': ['error', {max: 300, skipBlankLines: false, skipComments: false}],
        'max-lines-per-function': ['error', {max: 50, skipBlankLines: false, skipComments: false, IIFEs: true}],
        'max-nested-callbacks': ['error', 5],
        'new-cap': ['error'],
        'no-alert': ['warn'],
        'no-bitwise': ['error'],
        'no-caller': ['error'],
        'no-case-declarations': ['off'],
        'no-console': ['warn'],
        'no-div-regex': ['error'],
        'no-else-return': ['error', {allowElseIf: false}],
        'no-empty': ['error', {allowEmptyCatch: true}],
        'no-empty-static-block': ['error'],
        'no-eval': ['error'],
        'no-extend-native': ['error'],
        'no-extra-bind': ['error'],
        'no-extra-boolean-cast': ['error', {enforceForLogicalOperands: true}],
        'no-extra-label': ['error'],
        'no-implicit-coercion': ['error', {boolean: true, number: true, string: true, disallowTemplateShorthand: true}],
        'no-implicit-globals': ['error', {lexicalBindings: true}],
        'no-implied-eval': ['error'],
        'no-iterator': ['error'],
        'no-label-var': ['error'],
        'no-lone-blocks': ['error'],
        'no-lonely-if': ['error'],
        'no-multi-assign': ['error'],
        'no-multi-str': ['error'],
        'no-negated-condition': ['error'],
        'no-nested-ternary': ['error'],
        'no-new': ['error'],
        'no-new-func': ['error'],
        'no-new-wrappers': ['error'],
        'no-object-constructor': ['error'],
        'no-octal-escape': ['error'],
        'no-proto': ['error'],
        'no-return-assign': ['error', 'always'],
        'no-script-url': ['error'],
        'no-sequences': ['error', {allowInParentheses: false}],
        'no-underscore-dangle': [
            'error',
            {
                enforceInMethodNames: true,
                enforceInClassFields: true,
                allowInArrayDestructuring: false,
                allowInObjectDestructuring: false,
                allowFunctionParams: false,
                allowAfterThis: false,
                allowAfterSuper: false,
                allowAfterThisConstructor: false,
            },
        ],
        'no-unneeded-ternary': ['error'],
        'no-useless-call': ['error'],
        'no-useless-computed-key': ['error', {enforceForClassMembers: true}],
        'no-useless-concat': ['error'],
        'no-useless-rename': ['error'],
        'no-useless-return': ['error'],
        'no-var': ['error'],
        'no-void': ['error'],
        'object-shorthand': ['error', 'consistent'],
        'operator-assignment': ['error', 'always'],
        'prefer-arrow-callback': ['error', {allowNamedFunctions: false, allowUnboundThis: false}],
        'prefer-const': ['error'],
        'prefer-exponentiation-operator': ['error'],
        'prefer-named-capture-group': ['error'],
        'prefer-object-has-own': ['error'],
        'prefer-object-spread': ['error'],
        'prefer-promise-reject-errors': ['error'],
        'prefer-regex-literals': ['error'],
        'prefer-rest-params': ['error'],
        'prefer-spread': ['error'],
        'prefer-template': ['error'],
        'require-unicode-regexp': ['error'],
        'strict': ['error', 'never'],
        'symbol-description': ['error'],
        'yoda': ['error', 'never'],

        /*
         * TypeScript rules (the corresponding JavaScript rules have to be turned off)
         */
        'class-methods-use-this': ['off'],
        '@typescript-eslint/class-methods-use-this': ['error'],
        'default-param-last': ['off'],
        '@typescript-eslint/default-param-last': ['error'],
        'dot-notation': ['off'],
        '@typescript-eslint/dot-notation': ['error'],
        'max-params': ['off'],
        '@typescript-eslint/max-params': ['error', {max: 5}],
        'no-array-constructor': ['off'],
        '@typescript-eslint/no-array-constructor': ['error'],
        'no-empty-function': ['off'],
        '@typescript-eslint/no-empty-function': ['error'],
        'no-loop-func': ['off'],
        '@typescript-eslint/no-loop-func': ['error'],
        'no-magic-numbers': ['off'],
        '@typescript-eslint/no-magic-numbers': [
            'error',
            {
                ignore: [-1, 0, 1, 2],
                ignoreArrayIndexes: true,
                ignoreDefaultValues: true,
                ignoreClassFieldInitialValues: true,
                enforceConst: false,
                detectObjects: false,
                ignoreEnums: true,
                ignoreNumericLiteralTypes: true,
                ignoreReadonlyClassProperties: true,
                ignoreTypeIndexes: true,
            },
        ],
        'no-unused-expressions': ['off'],
        '@typescript-eslint/no-unused-expressions': ['error', {
            allowShortCircuit: false,
            allowTernary: true,
            allowTaggedTemplates: false,
            enforceForJSX: false,
        }],
        'no-use-before-define': ['off'],
        '@typescript-eslint/no-use-before-define': ['error', {
            functions: false,
            classes: true,
            variables: true,
            allowNamedExports: false,
        }],
        'no-useless-constructor': ['off'],
        '@typescript-eslint/no-useless-constructor': ['error'],
        'require-await': ['off'],
        '@typescript-eslint/require-await': ['error'],

        /*
         * Svelte rules
         */
        'svelte/infinite-reactive-loop': ['error'],
        'svelte/no-dom-manipulating': ['error'],
        'svelte/no-dupe-on-directives': ['error'],
        'svelte/no-dupe-use-directives': ['error'],
        'svelte/no-export-load-in-svelte-module-in-kit-pages': ['error'],
        'svelte/no-reactive-reassign': ['error'],
        'svelte/no-store-async': ['error'],
        'svelte/require-store-callbacks-use-set-param': ['error'],
        'svelte/require-store-reactive-access': ['error'],
        'svelte/valid-prop-names-in-kit-pages': ['error'],
        'svelte/block-lang': [
            'error',
            {
                enforceScriptPresent: false,
                enforceStylePresent: false,
                script: ['ts'],
                style: 'css',
            },
        ],
        'svelte/button-has-type': ['error'],
        'svelte/no-ignored-unsubscribe': ['error'],
        'svelte/no-immutable-reactive-statements': ['error'],
        'svelte/no-reactive-functions': ['error'],
        'svelte/no-reactive-literals': ['error'],
        'svelte/no-useless-mustaches': ['error'],
        'svelte/require-each-key': ['error'],
        'svelte/require-event-dispatcher-types': ['error'],
        'svelte/require-optimized-style-attribute': ['error'],
        'svelte/require-stores-init': ['error'],
        'svelte/derived-has-same-inputs-outputs': ['error'],
        'svelte/no-extra-reactive-curlies': ['error'],
        'svelte/prefer-class-directive': ['error'],
        'svelte/prefer-style-directive': ['error'],
        'svelte/shorthand-attribute': ['error', {prefer: 'never'}],
        'svelte/shorthand-directive': ['error', {prefer: 'never'}],
        'svelte/sort-attributes': [
            'error',
            {
                'order': [
                    'this',
                    'bind:this',
                    'id',
                    'name',
                    'slot',
                    'href',
                    'src',
                    'for',
                    'type',
                    {'match': '/^--/u', 'sort': 'alphabetical'}, // `--style-props`
                    ['style', '/^style:/u'], // `style` attribute, and `style:` directives
                    'class',
                    {'match': '/^class:/u', 'sort': 'alphabetical'}, // `class:` directives
                    {
                        'match': [  // other attributes, including props (don't sort these)
                            '!/:/u',
                            '!/^(?:this|id|name|style|class|href|src|for|type)$/u',
                            '!/^--/u',
                            '/^bind:/u', // `bind:` directives
                            '!bind:this',
                        ],
                        'sort': 'ignore',
                    },
                    ['/^on:/u'], // `on:` directives
                    {'match': '/^use:/u', 'sort': 'alphabetical'}, // `use:` directives
                    {'match': '/^transition:/u', 'sort': 'alphabetical'}, // `transition:` directive
                    {'match': '/^in:/u', 'sort': 'alphabetical'}, // `in:` directive
                    {'match': '/^out:/u', 'sort': 'alphabetical'}, // `out:` directive
                    {'match': '/^animate:/u', 'sort': 'alphabetical'}, // `animate:` directive
                    {'match': '/^let:/u', 'sort': 'alphabetical'}, // `let:` directives
                ],
            },
        ],
        'svelte/no-trailing-spaces': ['error', {skipBlankLines: false, ignoreComments: false}],
        // these rules conflict with svelte, so they need to be turned off
        'no-undef-init': ['off'],
        'no-throw-literal': ['off'],
        '@typescript-eslint/no-throw-literal': ['off'],

        /*
         * Other rules
         */
        'simple-import-sort/imports': ['error', {groups: [['^\\u0000', '^node:', '^@?\\w', '^', '^\\.']]}],
    },
};
