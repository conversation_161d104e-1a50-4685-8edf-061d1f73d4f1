<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('edition_descriptions', function (Blueprint $table) {
            $table->id();
            $table->longText('text');
            $table->foreignId('edition_id');
            $table->foreignId('language_id');
            $table->unique(['edition_id', 'language_id']);
            $table->string('translation_service');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('edition_descriptions');
    }
};
