<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('book_summaries', function (Blueprint $table) {
            $table->id();
            $table->longText('text');
            $table->foreignId('book_id');
            $table->foreignId('language_id');
            $table->unique(['book_id', 'language_id']);
            $table->string('translation_service');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('book_summaries');
    }
};
