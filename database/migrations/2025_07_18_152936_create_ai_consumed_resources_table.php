<?php

use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('ai_consumed_resources', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('input_tokens');
            $table->unsignedInteger('output_tokens');
            $table->foreignIdFor(User::class)->constrained('users');
            $table->date('date');
            $table->unique(['user_id', 'date']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('ai_consumed_resources');
    }
};
