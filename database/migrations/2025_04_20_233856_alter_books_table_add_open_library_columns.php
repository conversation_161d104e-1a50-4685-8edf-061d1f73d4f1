<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('editions', function (Blueprint $table) {
            $table->string('open_library_id')->nullable()->after('google_id');
            $table->unsignedDecimal('open_library_rating')->nullable()->after('google_ratings_count');
            $table->unsignedInteger('open_library_ratings_count')->after('open_library_rating');
            $table->unsignedInteger('open_library_reading_logs_count')->after('open_library_ratings_count');
        });
    }

    public function down(): void
    {
        Schema::table('editions', function (Blueprint $table) {
            $table->dropColumn('open_library_reading_logs_count');
            $table->dropColumn('open_library_ratings_count');
            $table->dropColumn('open_library_rating');
            $table->dropColumn('open_library_id');
        });
    }
};
