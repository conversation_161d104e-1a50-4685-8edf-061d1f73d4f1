/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
DROP TABLE IF EXISTS `achievement_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `achievement_user` (
  `achievement_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`achievement_id`,`user_id`),
  KEY `achievement_user_user_id_foreign` (`user_id`),
  CONSTRAINT `achievement_user_achievement_id_foreign` FOREIGN KEY (`achievement_id`) REFERENCES `achievements` (`id`),
  CONSTRAINT `achievement_user_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `achievements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `achievements` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `image` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `action_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `action_events` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `batch_id` char(36) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `actionable_type` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `actionable_id` bigint unsigned NOT NULL,
  `target_type` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `target_id` bigint unsigned NOT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `model_id` bigint unsigned DEFAULT NULL,
  `fields` text COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `status` varchar(25) COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'running',
  `exception` text COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `original` mediumtext COLLATE utf8mb4_0900_ai_ci,
  `changes` mediumtext COLLATE utf8mb4_0900_ai_ci,
  PRIMARY KEY (`id`),
  KEY `action_events_actionable_type_actionable_id_index` (`actionable_type`,`actionable_id`),
  KEY `action_events_target_type_target_id_index` (`target_type`,`target_id`),
  KEY `action_events_batch_id_model_type_model_id_index` (`batch_id`,`model_type`,`model_id`),
  KEY `action_events_user_id_index` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `audiobook_audiobook_subject`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `audiobook_audiobook_subject` (
  `audiobook_id` bigint unsigned NOT NULL,
  `audiobook_subject_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`audiobook_id`,`audiobook_subject_id`),
  KEY `audiobook_audiobook_subject_audiobook_subject_id_foreign` (`audiobook_subject_id`),
  CONSTRAINT `audiobook_audiobook_subject_audiobook_id_foreign` FOREIGN KEY (`audiobook_id`) REFERENCES `audiobooks` (`id`),
  CONSTRAINT `audiobook_audiobook_subject_audiobook_subject_id_foreign` FOREIGN KEY (`audiobook_subject_id`) REFERENCES `audiobook_subjects` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `audiobook_chapters`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `audiobook_chapters` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `url` text COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `audiobook_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `audiobook_chapters_audiobook_id_foreign` (`audiobook_id`),
  CONSTRAINT `audiobook_chapters_audiobook_id_foreign` FOREIGN KEY (`audiobook_id`) REFERENCES `audiobooks` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `audiobook_subjects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `audiobook_subjects` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `audiobooks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `audiobooks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `title` text COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `creator` text COLLATE utf8mb4_0900_ai_ci,
  `archive_identifier` text COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `archive_downloads_count` int unsigned NOT NULL DEFAULT '0',
  `archive_average_rating` int unsigned DEFAULT NULL,
  `archive_reviews_count` int unsigned NOT NULL DEFAULT '0',
  `publication_date` datetime NOT NULL,
  `language_id` bigint unsigned DEFAULT NULL,
  `book_id` bigint unsigned DEFAULT NULL,
  `views_count` int unsigned NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `audiobooks_uuid_unique` (`uuid`),
  KEY `audiobooks_uuid_index` (`uuid`),
  KEY `audiobooks_language_id_foreign` (`language_id`),
  KEY `audiobooks_book_id_foreign` (`book_id`),
  FULLTEXT KEY `audiobooks_title_fulltext` (`title`),
  FULLTEXT KEY `audiobooks_creator_fulltext` (`creator`),
  CONSTRAINT `audiobooks_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`),
  CONSTRAINT `audiobooks_language_id_foreign` FOREIGN KEY (`language_id`) REFERENCES `languages` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `author_book`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `author_book` (
  `author_id` bigint unsigned NOT NULL,
  `book_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`author_id`,`book_id`),
  KEY `author_book_author_id_index` (`author_id`),
  KEY `author_book_book_id_index` (`book_id`),
  CONSTRAINT `author_book_author_id_foreign` FOREIGN KEY (`author_id`) REFERENCES `authors` (`id`),
  CONSTRAINT `author_book_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `author_follows`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `author_follows` (
  `author_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`author_id`,`user_id`),
  KEY `author_follows_user_id_foreign` (`user_id`),
  CONSTRAINT `author_follows_author_id_foreign` FOREIGN KEY (`author_id`) REFERENCES `authors` (`id`),
  CONSTRAINT `author_follows_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `authors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `authors` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `authors_uuid_unique` (`uuid`),
  KEY `authors_uuid_index` (`uuid`),
  KEY `authors_name` (`name`(500))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `book_book_list`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `book_book_list` (
  `book_id` bigint unsigned NOT NULL,
  `book_list_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`book_id`,`book_list_id`),
  KEY `book_book_list_book_list_id_foreign` (`book_list_id`),
  CONSTRAINT `book_book_list_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`),
  CONSTRAINT `book_book_list_book_list_id_foreign` FOREIGN KEY (`book_list_id`) REFERENCES `book_lists` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `book_likes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `book_likes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `isPositive` tinyint(1) NOT NULL,
  `book_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `book_likes_book_id_foreign` (`book_id`),
  KEY `book_likes_user_id_foreign` (`user_id`),
  CONSTRAINT `book_likes_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`),
  CONSTRAINT `book_likes_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `book_lists`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `book_lists` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `book_lists_uuid_unique` (`uuid`),
  KEY `book_lists_uuid_index` (`uuid`),
  KEY `book_lists_user_id_foreign` (`user_id`),
  CONSTRAINT `book_lists_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `book_read_status_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `book_read_status_types` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `book_read_statuses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `book_read_statuses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `book_read_status_type_id` bigint unsigned NOT NULL,
  `book_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `book_read_statuses_book_read_status_type_id_foreign` (`book_read_status_type_id`),
  KEY `book_read_statuses_book_id_foreign` (`book_id`),
  KEY `book_read_statuses_user_id_foreign` (`user_id`),
  CONSTRAINT `book_read_statuses_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`),
  CONSTRAINT `book_read_statuses_book_read_status_type_id_foreign` FOREIGN KEY (`book_read_status_type_id`) REFERENCES `book_read_status_types` (`id`),
  CONSTRAINT `book_read_statuses_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `book_shares`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `book_shares` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `network` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `book_id` bigint unsigned NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `book_shares_book_id_foreign` (`book_id`),
  KEY `book_shares_user_id_foreign` (`user_id`),
  CONSTRAINT `book_shares_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`),
  CONSTRAINT `book_shares_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `book_subject`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `book_subject` (
  `book_id` bigint unsigned NOT NULL,
  `subject_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`book_id`,`subject_id`),
  KEY `book_subject_book_id_index` (`book_id`),
  KEY `book_subject_subject_id_index` (`subject_id`),
  CONSTRAINT `book_subject_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`),
  CONSTRAINT `book_subject_subject_id_foreign` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `books`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `books` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `views_count` int unsigned NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `books_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `edition_identifiers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `edition_identifiers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `number` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `edition_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `edition_identifiers_edition_id_foreign` (`edition_id`),
  KEY `edition_identifiers_number_index` (`number`),
  CONSTRAINT `edition_identifiers_edition_id_foreign` FOREIGN KEY (`edition_id`) REFERENCES `editions` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `editions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `editions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` text COLLATE utf8mb4_0900_ai_ci,
  `book_id` bigint unsigned NOT NULL,
  `isbn_13` varchar(255) COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `google_id` varchar(255) COLLATE utf8mb4_0900_as_cs DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `maturity_rating` varchar(255) COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `number_of_pages` int DEFAULT NULL,
  `subtitle` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
  `publish_date` varchar(255) COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `publish_year` smallint DEFAULT NULL,
  `publisher_id` bigint unsigned DEFAULT NULL,
  `language_id` bigint unsigned DEFAULT NULL,
  `cover` varchar(255) COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `binding` varchar(255) COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `edition` varchar(255) COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `dimensions` varchar(255) COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `msrp` decimal(8,2) unsigned DEFAULT NULL,
  `google_rating` decimal(8,2) unsigned DEFAULT NULL,
  `google_ratings_count` int unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `editions_book_id_index` (`book_id`),
  KEY `editions_publisher_id_foreign` (`publisher_id`),
  KEY `editions_language_id_foreign` (`language_id`),
  KEY `editions_publish_year_index` (`publish_year`),
  KEY `editions_isbn_13_index` (`isbn_13`),
  UNIQUE KEY `editions_google_id_index` (`google_id`),
  FULLTEXT KEY `editions_title_fulltext` (`title`),
  CONSTRAINT `editions_language_id_foreign` FOREIGN KEY (`language_id`) REFERENCES `languages` (`id`),
  CONSTRAINT `editions_publisher_id_foreign` FOREIGN KEY (`publisher_id`) REFERENCES `publishers` (`id`),
  CONSTRAINT `editions_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `failed_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `connection` text COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `queue` text COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `languages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `languages` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `code` varchar(255) COLLATE utf8mb4_0900_ai_ci NULL,
  `code_3_b` varchar(255) COLLATE utf8mb4_0900_ai_ci NULL,
  `code_3_t` varchar(255) COLLATE utf8mb4_0900_ai_ci NULL,
  `show_in_menu` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `languages_code_index` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `nova_field_attachments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nova_field_attachments` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `attachable_type` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `attachable_id` bigint unsigned NOT NULL,
  `attachment` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `disk` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `url` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `nova_field_attachments_attachable_type_attachable_id_index` (`attachable_type`,`attachable_id`),
  KEY `nova_field_attachments_url_index` (`url`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `nova_notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nova_notifications` (
  `id` char(36) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `notifiable_type` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `notifiable_id` bigint unsigned NOT NULL,
  `data` text COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `nova_notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `nova_pending_field_attachments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nova_pending_field_attachments` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `draft_id` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `attachment` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `disk` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `nova_pending_field_attachments_draft_id_index` (`draft_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `password_resets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `password_resets` (
  `email` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  KEY `password_resets_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `personal_access_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `tokenable_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `abilities` text COLLATE utf8mb4_0900_ai_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `publishers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `publishers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `publishers_name` (`name`(500))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `recommendation_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `recommendation_types` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `recommendations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `recommendations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned DEFAULT NULL,
  `book_id` bigint unsigned NOT NULL,
  `score` int NOT NULL,
  `recommendation_type_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `recommendations_user_id_foreign` (`user_id`),
  KEY `recommendations_book_id_foreign` (`book_id`),
  KEY `recommendations_recommendation_type_id_foreign` (`recommendation_type_id`),
  CONSTRAINT `recommendations_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`),
  CONSTRAINT `recommendations_recommendation_type_id_foreign` FOREIGN KEY (`recommendation_type_id`) REFERENCES `recommendation_types` (`id`),
  CONSTRAINT `recommendations_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `similar_books`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `similar_books` (
  `book_id` bigint unsigned NOT NULL,
  `similar_book_id` bigint unsigned NOT NULL,
  `points` int NOT NULL,
  `books_compare_limit` int unsigned NOT NULL DEFAULT '10',
  PRIMARY KEY (`book_id`,`similar_book_id`),
  KEY `similar_books_similar_book_id_foreign` (`similar_book_id`),
  CONSTRAINT `similar_books_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`),
  CONSTRAINT `similar_books_similar_book_id_foreign` FOREIGN KEY (`similar_book_id`) REFERENCES `books` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `similar_free_books`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `similar_free_books` (
  `book_id` bigint unsigned NOT NULL,
  `similar_free_book_id` bigint unsigned NOT NULL,
  `points` int NOT NULL,
  `books_compare_limit` int unsigned NOT NULL DEFAULT '10',
  PRIMARY KEY (`book_id`,`similar_free_book_id`),
  KEY `similar_free_books_similar_free_book_id_foreign` (`similar_free_book_id`),
  CONSTRAINT `similar_free_books_book_id_foreign` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`),
  CONSTRAINT `similar_free_books_similar_free_book_id_foreign` FOREIGN KEY (`similar_free_book_id`) REFERENCES `books` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `subjects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `subjects` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `show_in_menu` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `subjects_name` (`name`(500))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `user_activities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_activities` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `date` date NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `is_password_set` tinyint(1) NOT NULL,
  `google_sub` varchar(255) COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `apple_sub` varchar(255) COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `profile_picture` varchar(255) COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `language` varchar(255) COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `has_done_tutorial` tinyint NOT NULL DEFAULT '0',
  `register_ip` varchar(45) COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `new_email` varchar(255) COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`),
  UNIQUE KEY `users_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (1,'2014_10_12_000000_create_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (2,'2014_10_12_100000_create_password_resets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (3,'2018_01_01_000000_create_action_events_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (4,'2019_05_10_000000_add_fields_to_action_events_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (5,'2019_08_19_000000_create_failed_jobs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (6,'2019_12_14_000001_create_personal_access_tokens_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (7,'2021_08_25_193039_create_nova_notifications_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (8,'2021_12_07_190546_create_books_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (9,'2021_12_08_214425_create_google_subjects_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (10,'2021_12_08_222350_create_book_identifiers_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (11,'2021_12_29_141403_create_authors_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (12,'2021_12_29_142357_create_publishers_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (13,'2021_12_29_142901_create_languages_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (14,'2021_12_29_145206_alter_books_table_add_columns',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (15,'2021_12_29_151337_create_author_book_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (16,'2021_12_29_151555_create_book_google_subject_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (17,'2022_01_04_010613_alter_books_table_add_ol_fields',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (18,'2022_01_14_003846_alter_authors_table_add_columns',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (19,'2022_01_14_004544_create_author_photos',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (20,'2022_01_15_201915_alter_authors_table_add_index',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (21,'2022_01_16_234204_rename_book_tables_to_edition_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (22,'2022_01_17_005539_create_books_table_again',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (23,'2022_01_17_164551_create_book_likes_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (24,'2022_01_22_174037_alter_editions_table_add_book_id',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (25,'2022_01_22_204934_create_book_lists_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (26,'2022_01_22_205445_create_book_book_list_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (27,'2022_01_28_110912_alter_table_books_add_title_and_cover',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (28,'2022_02_09_210349_alter_table_users_add_social_login',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (29,'2022_02_10_230832_alter_table_users_add_profile_picture',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (30,'2022_02_12_174319_alter_authors_table_add_uuid',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (31,'2022_02_17_174427_create_recommendation_types_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (32,'2022_02_17_174642_create_recommendations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (33,'2022_02_23_003347_rename_google_subjects_tables_to_subjects_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (34,'2022_02_23_203350_create_author_external_ids_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (35,'2022_02_26_124027_create_author_follows_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (36,'2022_02_27_223855_alter_users_table_add_register_ip',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (37,'2022_02_28_194857_create_book_shares_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (38,'2022_03_05_201933_alter_subjects_table_add_ids',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (39,'2022_03_05_225737_alter_languages_table_add_name',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (40,'2022_03_06_200120_alter_book_and_edition_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (41,'2022_03_07_185009_alter_editions_table_add_open_library_date',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (42,'2022_03_07_211625_create_book_language_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (43,'2022_03_08_202242_create_author_links_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (44,'2022_03_09_235003_alter_users_table_add_uuid',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (45,'2022_03_12_015100_alter_tables_add_indexes',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (46,'2022_03_12_170704_alter_users_table_add_new_email',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (47,'2022_03_18_234502_alter_users_table_add_language',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (48,'2022_03_27_222741_alter_languages_table_add_show_column',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (49,'2022_03_30_231957_create_similar_books_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (50,'2022_04_21_231744_alter_tables_add_more_indexes',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (51,'2022_04_26_000000_add_fields_to_nova_notifications_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (52,'2022_05_14_165318_create_book_read_status_types',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (53,'2022_05_14_165437_create_book_read_statuses_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (54,'2022_05_23_182329_alter_similar_books_table_add_books_limit',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (55,'2022_05_25_213215_alter_edition_table_add_title_fulltext_index',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (56,'2022_06_05_153019_alter_book_shares_table_make_user_id_nullable',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (57,'2022_06_19_203448_alter_books_table_add_read_count',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (58,'2022_07_05_212813_create_audiobooks_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (59,'2022_07_05_212830_create_audiobook_subjects_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (60,'2022_07_05_212845_create_audiobook_audiobook_subject_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (61,'2022_07_09_201208_create_audiobook_chapters_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (62,'2022_07_27_184136_alter_audiobook_tables_add_indexes',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (63,'2022_08_05_095006_alter_editions_table_add_publication_year',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (64,'2022_08_05_131651_alter_editions_table_add_publication_year_index',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (65,'2022_08_05_190604_alter_books_table_add_uuid',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (66,'2022_08_06_204039_alter_editions_table_add_has_cover',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (67,'2022_08_07_125203_alter_audiobooks_table_add_views_count',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (68,'2022_08_12_093457_alter_subjects_table_add_show_column',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (69,'2022_09_07_215618_create_similar_free_books_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (70,'2022_09_09_011746_alter_books_table_add_is_free_index',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (71,'2022_12_19_000000_create_field_attachments_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (72,'2022_12_27_150949_alter_tables_add_missing_dates',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (73,'2023_01_18_193247_alter_tables_add_is_imported',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (74,'2023_03_18_011012_alter_tables_add_deleted_at_column',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (75,'2023_03_25_222831_create_achievements_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (76,'2023_03_26_120418_create_achievement_user_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (77,'2023_09_09_234725_alter_editions_table_add_more_fields',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (78,'2023_09_11_235222_alter_edition_identifiers_table_add_number_index',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (79,'2023_10_12_180212_alter_users_table_add_last_active_at',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (80,'2023_10_13_215729_alter_users_table_add_has_done_tutorial',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (81,'2023_11_25_150829_alter_users_table_add_remember_token',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (82,'2023_11_25_181418_create_user_activities_table_and_migrate_users_last_active_at',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (83,'2024_01_04_203832_alter_personal_access_tokens_table_add_expires_at',1);
