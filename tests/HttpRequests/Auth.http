POST http://127.0.0.1:8000/register
Content-Type: application/json
Accept: application/json

{
    "name": "name",
    "email": "<EMAIL>",
    "password": "password"
}

###

POST http://127.0.0.1:8000/login
Content-Type: application/json
Accept: application/json

{
    "email": "<EMAIL>",
    "password": "password"
}

###

POST http://127.0.0.1:8000/logout
Content-Type: application/json
Accept: application/json
Authorization: Bearer 31|vVPIGlNAKEe8ziuDVfPb6QbTVM8QUfXI2uYKX4Cj

{}
