<?php declare(strict_types=1);

namespace Tests\Feature\Commands;

use Google\Client as GoogleClient;
use GuzzleHttp\Client;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Mockery;
use Tests\TestCase;

final class ImportFreeBooksCommandTest extends TestCase
{
    use RefreshDatabase;

    public function testCommand(): void
    {
        $mockHandler = new MockHandler();
        $handlerStack = HandlerStack::create($mockHandler);
        $client = new Client(['handler' => $handlerStack]);

        $mock = Mockery::mock(GoogleClient::class, [])->makePartial();
        $mock->shouldAllowMockingProtectedMethods()
            ->shouldReceive('createDefaultHttpClient')
            ->andReturn($client);
        $this->instance(GoogleClient::class, $mock);

        $mockHandler->reset();
        $mockHandler->append(new Response(200, [], $this->getResponseWithOneBook()));
        $mockHandler->append(new Response(200, [], $this->getEmptyResponse()));
        $mockHandler->append(new Response(200, [], $this->getResponseWithOneBook()));
        $mockHandler->append(new Response(200, [], $this->getEmptyResponse()));

        DB::table('google_subjects')->insert([
            'id' => 1,
            'name' => 'Subject name',
            'is_imported' => 0,
            'is_auto_added' => 0,
            'created_at' => '2000-01-01 00:00:00',
            'updated_at' => '2000-01-01 00:00:00',
        ]);

        $this->artisan('books:import-free')
            ->expectsOutput('The import for subject Subject name was successful! 1 books were imported.')
            ->expectsOutput('The import for subject Reference was successful! 0 books were imported.')
            ->assertExitCode(0);

        $this->assertDatabaseHas('books', [
            'id' => 1,
            'google_id' => 'zYw3sYFtz9kC',
            'google_title' => 'The Contemporary Thesaurus of Search Terms and Synonyms',
            'google_is_free' => 1,
        ]);
        $this->assertDatabaseHas('book_identifiers', [
            'number' => '157356107X',
            'type' => 'ISBN_10',
            'book_id' => 1,
        ]);
        $this->assertDatabaseHas('book_identifiers', [
            'number' => '9781573561075',
            'type' => 'ISBN_13',
            'book_id' => 1,
        ]);
        $this->assertDatabaseHas('google_subjects', [
            'name' => 'Subject name',
            'is_imported' => 1,
            'is_auto_added' => 0,
        ]);
        $this->assertDatabaseHas('google_subjects', [
            'name' => 'Reference',
            'is_imported' => 1,
            'is_auto_added' => 1,
        ]);
    }

    private function getResponseWithOneBook(): string
    {
        return '{
            "kind": "books#volumes",
            "totalItems": 845,
            "items": [
                {
                    "kind": "books#volume",
                    "id": "zYw3sYFtz9kC",
                    "etag": "NYLidWfQM0o",
                    "selfLink": "https://www.googleapis.com/books/v1/volumes/zYw3sYFtz9kC",
                    "volumeInfo": {
                        "title": "The Contemporary Thesaurus of Search Terms and Synonyms",
                        "subtitle": "A Guide for Natural Language Computer Searching",
                        "authors": [
                            "Sara D. Knapp"
                        ],
                        "publisher": "Greenwood Publishing Group",
                        "publishedDate": "2000",
                        "description": "Whether your search is limited to a single database or is as expansive as all of cyberspace, you won\'t find the intended results unless you use the words that work. Now in its second edition, Sara Knapp has updated and expanded this invaluable resource. Unlike any other thesaurus available, this popular guide offers a wealth of natural language options in a convenient, A-to-Z format. It\'s ideal for helping users find the appropriate word or words for computer searches in the humanities, social sciences, and business. The second edition has added more than 9,000 entries to the first edition\'s extensive list. Now, the Thesaurus contains almost 21,000 search entries! New or expanded areas include broader coverage of business terms and humanities-including arts literature, philosophy, religion, and music.",
                        "industryIdentifiers": [
                            {
                                "type": "ISBN_10",
                                "identifier": "157356107X"
                            },
                            {
                                "type": "ISBN_13",
                                "identifier": "9781573561075"
                            }
                        ],
                        "readingModes": {
                            "text": false,
                            "image": true
                        },
                        "pageCount": 682,
                        "printType": "BOOK",
                        "categories": [
                            "Reference"
                        ],
                        "averageRating": 3,
                        "ratingsCount": 1,
                        "maturityRating": "NOT_MATURE",
                        "allowAnonLogging": false,
                        "contentVersion": "1.2.2.0.preview.1",
                        "panelizationSummary": {
                            "containsEpubBubbles": false,
                            "containsImageBubbles": false
                        },
                        "imageLinks": {
                        "smallThumbnail": "http://books.google.com/books/content?id=zYw3sYFtz9kC&printsec=frontcover&img=1&zoom=5&edge=curl&source=gbs_api",
                          "thumbnail": "http://books.google.com/books/content?id=zYw3sYFtz9kC&printsec=frontcover&img=1&zoom=1&edge=curl&source=gbs_api"
                        },
                        "language": "en",
                        "previewLink": "http://books.google.ro/books?id=zYw3sYFtz9kC&pg=PR21&dq=search+terms&hl=&cd=1&source=gbs_api",
                        "infoLink": "http://books.google.ro/books?id=zYw3sYFtz9kC&dq=search+terms&hl=&source=gbs_api",
                        "canonicalVolumeLink": "https://books.google.com/books/about/The_Contemporary_Thesaurus_of_Search_Ter.html?hl=&id=zYw3sYFtz9kC"
                    },
                    "saleInfo": {
                        "country": "RO",
                        "saleability": "NOT_FOR_SALE",
                        "isEbook": false
                    },
                    "accessInfo": {
                        "country": "RO",
                        "viewability": "PARTIAL",
                        "embeddable": true,
                        "publicDomain": false,
                        "textToSpeechPermission": "ALLOWED",
                        "epub": {
                            "isAvailable": false
                        },
                        "pdf": {
                            "isAvailable": true,
                            "acsTokenLink": "http://books.google.ro/books/download/The_Contemporary_Thesaurus_of_Search_Ter-sample-pdf.acsm?id=zYw3sYFtz9kC&format=pdf&output=acs4_fulfillment_token&dl_type=sample&source=gbs_api"
                        },
                        "webReaderLink": "http://play.google.com/books/reader?id=zYw3sYFtz9kC&hl=&printsec=frontcover&source=gbs_api",
                        "accessViewStatus": "SAMPLE",
                        "quoteSharingAllowed": false
                    },
                    "searchInfo": {
                        "textSnippet": "Natural Language \u003cb\u003eSearching\u003c/b\u003e xxi to specify the \u003cb\u003eword\u003c/b\u003e order as well . For details , consult system documentation . different meaning when you are talking about racing than it does when you are talking about food ."
                    }
                }
            ]
        }';
    }

    private function getEmptyResponse(): string
    {
        return '{
            "kind": "books#volumes",
            "totalItems": 845,
            "items": []
        }';
    }
}
