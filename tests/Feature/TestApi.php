<?php declare(strict_types=1);

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

trait TestApi
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        foreach ($this->database as $tableName => $tableData) {
            DB::table($tableName)->insert($tableData);
        }

        foreach ($this->http as $url => $response) {
            Http::fake([$url => Http::response($response)]);
        }

        $this->withHeaders([
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ]);
    }

    /**  @dataProvider dataSet */
    public function test_api(
        array $request,
        array $requestHeaders,
        array $requestDatabase,
        array $requestHttp,
        int $responseCode,
        array $response,
        array $responseCounts,
        array $database,
        array $databaseMissing
    ): void {
        $this->withHeaders($requestHeaders);

        foreach ($requestDatabase as $tableName => $tableData) {
            DB::table($tableName)->insert($tableData);
        }

        foreach ($requestHttp as $url => $httpResponse) {
            Http::fake([$url => Http::response($httpResponse)]);
        }

        $method = $this->method . 'Json';
        if ($method === 'getJson') {
            $glue = Str::contains($this->url, '?') ? '&' : '?';
            $this->url .= $glue . http_build_query($request);
            $request = [];
        }
        $api = $this->{$method}($this->url, $request);
        $api->assertStatus($responseCode)->assertJsonStructure($response);

        foreach ($responseCounts as $responseKey => $responseCount) {
            $api->assertJsonCount($responseCount, $responseKey);
        }

        foreach ($database as $tableName => $tableData) {
            $this->assertDatabaseHas($tableName, $tableData);
        }

        foreach ($databaseMissing as $tableName => $tableData) {
            $this->assertDatabaseMissing($tableName, $tableData);
        }
    }

    public function dataSet(): array
    {
        return $this->cases;
    }
}
