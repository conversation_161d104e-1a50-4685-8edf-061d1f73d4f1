<?php declare(strict_types=1);

namespace Tests\Feature\Authentication;

use Tests\Feature\TestApi;
use Tests\TestCase;

final class RegisterTest extends TestCase
{
    use TestApi;

    public string $method = 'post';
    public string $url = '/register';
    public array $database = [];
    public array $http = [];
    public array $cases = [
        [
            'request' => [
                'name' => 'name',
                'email' => '<EMAIL>',
                'password' => 'password',
            ],
            'requestHeaders' => [],
            'requestDatabase' => [],
            'requestHttp' => [],
            'statusCode' => 200,
            'response' => [
                'token',
            ],
            'responseCount' => [],
            'database' => [
                'users' => [
                    'name' => 'name',
                    'email' => '<EMAIL>',
                ],
            ],
            'databaseMissing' => [],
        ],
        [
            'request' => [
                'name' => 'name',
                'email' => 'email',
                'password' => 'password',
            ],
            'requestHeaders' => [],
            'requestDatabase' => [],
            'requestHttp' => [],
            'statusCode' => 422,
            'response' => [
                'errors' => [
                    'email',
                ],
            ],
            'responseCount' => [],
            'database' => [],
            'databaseMissing' => [],
        ],
        [
            'request' => [
                'name' => 'name',
                'email' => '<EMAIL>',
                'password' => 'pass',
            ],
            'requestHeaders' => [],
            'requestDatabase' => [],
            'requestHttp' => [],
            'statusCode' => 422,
            'response' => [
                'errors' => [
                    'password',
                ],
            ],
            'responseCount' => [],
            'database' => [],
            'databaseMissing' => [],
        ],
        [
            'request' => [],
            'requestHeaders' => [],
            'requestDatabase' => [],
            'requestHttp' => [],
            'statusCode' => 422,
            'response' => [
                'errors' => [
                    'name',
                    'email',
                    'password',
                ],
            ],
            'responseCount' => [],
            'database' => [],
            'databaseMissing' => [],
        ],
        [
            'request' => [
                'name' => 'name',
            ],
            'requestHeaders' => [],
            'requestDatabase' => [],
            'requestHttp' => [],
            'statusCode' => 422,
            'response' => [
                'errors' => [
                    'email',
                    'password',
                ],
            ],
            'responseCount' => [],
            'database' => [],
            'databaseMissing' => [],
        ],
        [
            'request' => [
                'email' => '<EMAIL>',
            ],
            'requestHeaders' => [],
            'requestDatabase' => [],
            'requestHttp' => [],
            'statusCode' => 422,
            'response' => [
                'errors' => [
                    'name',
                    'password',
                ],
            ],
            'responseCount' => [],
            'database' => [],
            'databaseMissing' => [],
        ],
        [
            'request' => [
                'password' => 'password',
            ],
            'requestHeaders' => [],
            'requestDatabase' => [],
            'requestHttp' => [],
            'statusCode' => 422,
            'response' => [
                'errors' => [
                    'name',
                    'email',
                ],
            ],
            'responseCount' => [],
            'database' => [],
            'databaseMissing' => [],
        ],
    ];
}
