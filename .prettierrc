{"printWidth": 120, "tabWidth": 4, "useTabs": false, "semi": true, "singleQuote": true, "quoteProps": "consistent", "jsxSingleQuote": true, "trailingComma": "all", "bracketSpacing": false, "bracketSameLine": false, "arrowParens": "always", "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": true, "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "singleAttributePerLine": false, "plugins": ["prettier-plugin-svelte", "prettier-plugin-tailwindcss"], "overrides": [{"files": "*.svelte", "options": {"parser": "svelte", "svelteAllowShorthand": false}}]}