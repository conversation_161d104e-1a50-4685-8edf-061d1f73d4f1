{"name": "liberom/liberom", "type": "project", "description": "Liberom", "keywords": ["liberom"], "license": "MIT", "require": {"php": "8.2.*", "ext-bcmath": "*", "ext-pdo": "*", "ext-zlib": "*", "aws/aws-sdk-php": "^3", "biiiiiigmonster/hasin": "1.0", "google/cloud-translate": "^2.0", "guzzlehttp/guzzle": "^7.0.1", "http-interop/http-factory-guzzle": "^1.2", "laravel/framework": "^10.0", "laravel/horizon": "^5.9", "laravel/nova": "^4.0", "laravel/sanctum": "^3.2", "laravel/scout": "^9.4", "laravel/tinker": "^2.5", "meilisearch/meilisearch-php": "^0.24.2", "spatie/laravel-http-logger": "^1.10", "spatie/laravel-sitemap": "^6.2", "stevebauman/location": "^6.3", "stichoza/google-translate-php": "^5.2"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.10", "spatie/laravel-ignition": "^2.0", "fakerphp/faker": "^1.9.1", "itsgoingd/clockwork": "^5.1", "laravel/pint": "^1.5", "laravel/sail": "^1.0.1", "laravel/telescope": "^4.6", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "platform": {"ext-pcntl": "8.0", "ext-posix": "8.0"}, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "prefer-stable": true, "repositories": {"nova": {"type": "composer", "url": "https://nova.laravel.com"}}}