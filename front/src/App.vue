<template>
    <div :class="audiobookPlayer.enabled ? 'audiobook-player-open' : ''">
        <router-view :key="$route.fullPath"></router-view>
        <vue-progress-bar></vue-progress-bar>
        <AudiobookPlayer v-if="audiobookPlayer.enabled" />
    </div>
</template>

<script>
    import {core} from './config/pluginInit';
    import {mapGetters} from 'vuex';
    import AudiobookPlayer from './components/AudiobookPlayer';
    import settings from "@/config/settings";

    export default {
        components: {
            AudiobookPlayer,
        },
        created: function () {
            core.mainIndex();
        },
        mounted: function () {},
        computed: {
            ...mapGetters({
                audiobookPlayer: 'Setting/audiobookPlayerState',
            }),
        },
        methods: {},
        metaInfo: function () {
            return {
                title: 'Home',
                titleTemplate: '%s | ' + settings.name,
                htmlAttrs: {
                    lang: 'en',
                },
                meta: [
                    {property: 'description', content: this.$t('meta.description')},
                ],
            };
        },
    };
</script>

<style lang="scss">
    @import "assets/scss/style.scss";
</style>
