{"accountSettings": {"accountSettings": "Paramètres du compte", "accountSettingsSaved": "Les paramètres du compte ont été enregistrés.", "alarm": "Alarme", "changePassword": "Modifier le mot de passe", "changeProfilePicture": "Modifier la photo du profil", "currentPassword": "Mot de passe actuel", "email": "E-mail:", "enableAlarm": "Activer l'alarme de lecture", "imageUpload": "Téléchargement d'images", "language": "Langue:", "name": "Nom:", "newPassword": "Nouveau mot de passe", "passwordSaved": "Votre nouveau mot de passe a été enregistré.", "readAlarm": "<PERSON><PERSON><PERSON>", "readNotificationQuote1": "« Un lecteur vit mille vies avant de mourir. L'homme qui ne lit jamais ne vit qu'une fois. » - <PERSON><PERSON>", "readNotificationQuote2": "« Un livre est un cadeau que vous pouvez ouvrir encore et encore. » - <PERSON>", "readNotificationQuote3": "« Ne remettez jamais à demain le livre que vous pouvez lire aujourd'hui. » - <PERSON><PERSON><PERSON>", "readNotificationTitle": "<PERSON><PERSON><PERSON>", "savePassword": "Enregistrer", "saveSettings": "Enregistrer", "setAlarm": "Définir une alarme de lecture. Vous recevrez une notification de rappel à ce moment-là.", "verificationEmailSent": "Un e-mail a été envoyé à votre nouveau compte. Veuillez utiliser le lien pour vérifier votre compte de messagerie."}, "addList": {"addList": "Ajouter une liste", "error": "Une erreur s'est produite. Veuillez réessayer.", "listName": "Nom de la liste:", "nameLengthError": "Le nom ne peut pas dépasser 200 lettres.", "nameRequiredError": "Le nom est obligatoire.", "submit": "So<PERSON><PERSON><PERSON>"}, "auth": {"backToApp": "Retour à l'appli", "changePassword": "Modifier le mot de passe", "clickHere": "cliquer ici", "confirmPassword": "Confirmer le mot de passe", "emailTaken": "L'e-mail a déjà été pris.", "emailVerification": "Vérification de l'e-mail", "emailVerified": "Votre e-mail a été vérifié.", "error": "Une erreur s'est produite.", "forgotPassword": "Mot de passe oublié?", "haveAccount": "Vous avez déjà un compte?", "ifForgottenPassword": "Si vous avez oublié votre mot de passe", "login": "Se connecter", "noAccount": "Vous n'avez pas de compte?", "passwordChange": "Modification du mot de passe", "passwordChanged": "Votre mot de passe a été modifié.", "passwordConfirmationError": "Les mots de passe ne correspondent pas.", "resetPassword": "Réinitialiser le mot de passe", "resetPasswordEmail": "Saisissez votre adresse de messagerie et nous vous enverrons un e-mail contenant les instructions afin de réinitialiser votre mot de passe.", "resetPasswordEmailSent": "Si ce compte de messagerie est lié à un compte Liberom, un e-mail contenant un lien de réinitialisation du mot de passe a été envoyé.", "setNewPassword": "Définir un nouveau mot de passe", "signup": "S'enregistrer", "verificationEmailError": "Une erreur s'est produite lors de la vérification de votre e-mail.", "verificationEmailSent": "Un e-mail de vérification a été envoyé à votre nouveau compte. Veuillez utiliser le lien pour vérifier votre compte de messagerie."}, "author": {"books": "Livres", "follow": "Suivre", "subjects": "Sujets:", "unfollow": "Ne pas suivre"}, "book": {"addList": "Ajouter une liste de livre", "addTo": "Ajouter à", "audiobook": "Écouter gratuitement", "buyAmazon": "Acheter sur Amazon", "by": "par", "dislike": "N'aime pas", "goToBook": "Voir le livre", "languages": "<PERSON><PERSON>", "like": "<PERSON><PERSON>", "numberOfPages": "Nombre de pages", "preview": "Prévisualisation", "publishDate": "Année de publication", "read": "<PERSON>re gratuitement", "share": "Partager:", "similarBooks": "<PERSON><PERSON> similaires", "subjects": "Sujets"}, "bookStatuses": {"currentlyReading": "Lecture en cours", "dropped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON>", "readStatus": "Statut de lecture", "readStatusSelect": "-- <PERSON><PERSON><PERSON> de lecture --", "wantToRead": "Envie de lire"}, "fields": {"email": "Adresse e-mail", "name": "Nom", "password": "Mot de passe"}, "header": {"accountSettings": "Paramètres du compte", "darkTheme": "Thème sombre", "hello": "Bonjour", "logout": "Se déconnecter", "search": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "Recherche d'un titre de livre"}, "lists": {"authorsFollowed": "Auteurs que vous avez suivis", "createList": "<PERSON><PERSON>er une nouvelle liste", "liked": "J'aime bien", "seeAll": "Voir tout"}, "menu": {"accountSettings": "Paramètres du compte", "addBookList": "Ajouter une liste de livres", "author": "<PERSON><PERSON><PERSON>", "book": "Livre", "bookLists": "Listes de livres", "bookReader": "Lecteur de livres", "freeAudiobooks": "Livres audio gratuits", "freeBooks": "<PERSON>res gratuits", "homepage": "Page d'accueil", "recommendations": "Recommandations", "search": "<PERSON><PERSON><PERSON>", "viewBookList": "Voir la liste des livres"}, "meta": {"description": "Trouvez et lisez plus de 700.000 livres ou écoutez plus de 18.000 livres audio, le tout gratuitement. Vous pouvez également aimer, ne pas aimer, partager et organiser les livres en listes (gratuits ou non). Vous recevrez des recommandations personnalisées en fonction de vos goûts et de vos auteurs préférés."}, "recommendations": {"betterRecommendations": "Afin de connaitre vos préférences et pour de meilleures recommandations, dites-nous quels livres vous aimez ou n'aimez pas.", "freeBooksLikes": "Des livres gratuits comme ceux que vous avez aimés", "moreBooksAuthors": "Plus de livres d'auteurs que vous avez suivis", "moreBooksLikes": "Plus de livres comme ceux que vous avez aimés", "moreBooksSubjects": "Plus de livres sur des sujets que vous aimez", "popularBooks": "Livres à succès", "recommendations": "Recommandations", "searchBooks": "Rechercher des livres", "topThreeRecommendations": "Les 3 principales recommandations"}, "search": {"author": "<PERSON><PERSON><PERSON>", "freeBooks": "Afficher uniquement les livres gratuits", "isbn": "ISBN", "language": "<PERSON><PERSON>", "maxPublishYear": "Année maximale de publication", "minPublishYear": "Année minimale de publication", "search": "<PERSON><PERSON><PERSON>", "subject": "Sujet", "title": "Titre"}, "tutorial": {"closePlayer": "Vous pouvez cliquer sur le bouton X pour fermer le lecteur.", "end": "Mais nous vous laisserons découvrir tout cela par vous-même. N'oubliez pas d'explorer, de vous connecter et de partager vos aventures littéraires avec d'autres amateurs de livres. <PERSON><PERSON> lecture !", "finish": "Fin", "freeAudiobooks": "Profitez de livres audio du domaine public ! Choisissez un livre audio que vous pensez apprécier.", "freeBooks": "Tous les livres qui sont dans le domaine public sont disponibles à la lecture. Plongez dans des chefs-d'œuvre classiques et des histoires intemporelles et déverrouillez un monde de littérature gratuite !", "goToAudiobooks": "Voyons maintenant la section des livres audio gratuits.", "goToRecommended": "Allons maintenant à la page des recommandations pour voir quels livres vous sont recommandés.", "likeBook": "Évaluez vos livres préférés en cliquant sur le bouton 'J'aime' et faites-nous part de vos préférences pour de meilleures recommandations ! Allez-y et aimez ce livre maintenant (vous pouvez annuler cela plus tard).", "listenToFirstChapter": "Cliquez maintenant sur le premier chapitre pour commencer à écouter.", "nextButton": "Suivant", "otherFeatures": "Vous êtes prêt à embarquer pour votre voyage de lecture avec Liberom ! Nous avons également d'autres fonctionnalités telles que la définition d'un statut de lecture pour chaque livre ou la création de listes personnalisées pour mieux organiser vos livres. Vous pouvez également définir une alarme pour vous rappeler de lire chaque jour.", "readBook": "Cliquez maintenant sur le bouton pour lire le livre gratuitement !", "recommendations": "Explorez une sélection soigneusement choisie de livres juste pour vous ! Plongez dans une liste personnalisée de recommandations basée sur vos préférences de lecture. Plus vous aimez ou n'aimez pas les livres, meilleures seront les recommandations.", "searchForm": "Vous pouvez utiliser le formulaire de recherche pour accéder instantanément à une vaste collection de livres en effectuant des recherches par titres, auteurs et d'autres types de filtres.", "selectBook": "Sélectionnez un livre qui vous intéresse dans la liste des résultats.", "welcome": "Bienvenue sur Liberom, votre application incontournable pour découvrir, organiser et apprécier une collection diversifiée de livres. Démarrez votre parcours littéraire en cliquant sur le bouton ci-dessous.", "welcomeButton": "Commencer", "welcomeTitle": "Bienvenue"}, "viewList": {"confirmDelete": "Confirmer la <PERSON>", "confirmDeleteQuestion": "Êtes-vous sûr de vouloir supprimer cette liste?", "delete": "<PERSON><PERSON><PERSON><PERSON>", "listChanged": "Le nom de la liste a été modifié.", "listName": "Nom de la liste", "listUpdate": "Actualisation de la liste", "nameLengthError": "Le nom ne peut pas dépasser 200 lettres.", "nameRequiredError": "Le nom est obligatoire.", "update": "Actualisation"}}