// Bootstrap Css
//@import "~bootstrap/scss/bootstrap";
//@import "~bootstrap/scss/bootstrap-grid";
@import url('https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900&display=swap');

// Animate css
@import "~animate.css/animate.min.css";

@import '~node-snackbar/src/sass/snackbar.sass';

[dir="ltr"] {
  &[mode="light"] {
    @import "../css/bootstrap";
    @import "../css/light/typography";
    @import "../css/light/style";
    @import "../css/light/style-customizer.scss";
    @import "../css/light/responsive";

    .right-sidebar-panel {
      background: var(--iq-white);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
      height: 100vh;
      padding: 15px;
      overflow-y: scroll;
    }

    .right-sidebar-toggle {
      background: var(--iq-white);
    }
  }

  &[mode="dark"] {
    @import "../css/bootstrap";
    @import "../css/dark/typography";
    @import "../css/dark/style";
    @import "../css/dark/style-customizer.css";
    @import "../css/dark/responsive";

    .right-sidebar-panel {
      background: var(--iq-dark-body-bg);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
      height: 100vh;
      padding: 15px;
      overflow-y: scroll;
    }

    .right-sidebar-toggle {
      background: var(--iq-dark-card);
    }
  }

  .right-sidebar-mini {
    top: 0;
    z-index: 100;
    position: fixed;
    width: 300px;
    right: 0;
    transform: translateX(calc(111% + -2em));
    transition: all 0.5s ease;
  }

  .right-sidebar-mini .side-right-icon {
    display: none;
  }

  .right-sidebar-toggle {
    position: absolute;
    margin-left: -44px;
    background: var(--iq-white);
    padding: 8px;
    width: 45px;
    height: 42px;
    display: inline;
    top: 200px;
    z-index: 99;
    cursor: pointer;
    left: 0px;
    right: auto;
    -webkit-box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.1);
    box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.1);
  }

  .right-sidebar {
    transform: translateX(calc(10% + -1em));
  }

  .right-sidebar .side-left-icon {
    display: none;
  }

  .right-sidebar .side-right-icon {
    display: block;
  }

  .right-sidebar-mini p {
    font-size: 16px;
    line-height: 24px;
    margin-top: 0;
    margin-bottom: 1rem;
  }

  .right-sidebar-mini .right-sidebar-toggle i {
    color: var(--iq-primary);
    text-align: center;
  }

  .sub-color {
    color: var(--iq-title-text);
    font-size: 20px;
    margin-top: 0px;
    font-weight: 600;
  }

  .right-sidebar-mini div .iq-colormark:before {
    position: absolute;
    content: "\f00c";
    left: 50%;
    top: 5%;
    color: var(--iq-white);
    font-size: 20px;
    font-weight: 900;
    display: inline-block;
    text-align: center;
    font-family: "Font Awesome 5 Free";
    transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
  }
}

[dir="rtl"] {
  &[mode="light"] {
    @import "../css/rtl/bootstrap";
    @import "../css/rtl/light/typography";
    @import "../css/rtl/light/style";
    @import "../css/rtl/light/style-customizer.css";
    @import "../css/rtl/light/responsive";

    .right-sidebar-panel {
      background-color: var(--iq-white);
      box-shadow: 0px 0px 25px 0px rgba(45, 69, 95, 0.1);
      height: 100vh;
      padding: 15px;
      overflow-y: scroll;
    }

    .right-sidebar-toggle {
      background: var(--iq-white);
    }
  }

  &[mode="dark"] {
    @import "../css/rtl/bootstrap";
    @import "../css/rtl/dark/typography";
    @import "../css/rtl/dark/style";
    @import "../css/rtl/dark/responsive";

    .right-sidebar-panel {
      background-color: var(--iq-dark-body-bg);
      box-shadow: 0px 0px 25px 0px rgba(45, 69, 95, 0.1);
      height: 100vh;
      padding: 15px;
      overflow-y: scroll;
    }

    .right-sidebar-toggle {
      background: var(--iq-dark-card);
    }
  }

  /* right sidebar */
  .iq-right-fixed {
    margin: 0 15px;
  }

  .right-sidebar-mini {
    top: 0;
    z-index: 100;
    position: fixed;
    width: 300px;
    left: 0;
    transform: translateX(calc(-1 * (111% + -2em)));
    transition: all 0.5s ease;
  }

  .right-sidebar-mini .side-left-icon {
    display: none;
  }

  .right-sidebar-toggle {
    position: absolute;
    margin-right: -44px;
    padding: 8px;
    top: 50%;
    z-index: 99;
    box-shadow: 10px 5px 20px rgba(0, 0, 0, 0.19);
    cursor: pointer;
    padding: 8px;
    width: 45px;
    height: 42px;
  }

  .right-sidebar-mini .right-sidebar-toggle i {
    color: var(--iq-primary);
    text-align: center;
  }

  .right-sidebar {
    transform: translateX(calc(-1 * (10% + -1em)));
  }

  .right-sidebar .side-right-icon {
    display: none;
  }

  .right-sidebar .side-left-icon {
    display: block;
  }

  .right-sidebar-toggle span {
    display: none !important;
  }

  .right-sidebar-mini div .iq-colormark:before {
    position: absolute;
    content: "\f00c";
    left: 50%;
    top: 5%;
    color: var(--iq-white);
    font-size: 20px;
    font-weight: 900;
    display: inline-block;
    text-align: center;
    font-family: "Font Awesome 5 Free";
    transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
  }
}


.iq-global-search > ul > li a, .slick-arrow, .breadcrumb-item > a {
  color: var(--iq-primary);

  &:hover {
    color: var(--iq-primary);
  }
}

// Font awesome icon Css
@import "~@fortawesome/fontawesome-free/css/all.min.css";
