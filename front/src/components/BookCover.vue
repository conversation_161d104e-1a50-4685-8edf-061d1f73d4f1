<template>
    <img v-if="cover" class="img-fluid rounded w-cover" :src="cover" alt="cover">
    <div v-else class="img-fluid rounded w-cover h-100 bg-gray black default-cover">
        <i :class="'fas fa-book-open mb-3' + (title === '' ? '' : ' mt-3')"></i>
        <h5 v-if="title" class="px-2">{{ truncateTitle(title) }}</h5>
    </div>
</template>

<script>
    export default {
        name: 'BookCover',
        props: {
            cover: String,
            title: String,
        },
        methods: {
            truncateTitle: function (str) {
                return (str.length > 100) ? str.substr(0, 100) + '...' : str;
            },
        },
    };
</script>

<style lang="scss">
    .w-cover {
        width: 250px;
        min-height: 300px;
    }

    .default-cover {
        display: flex;
        flex-direction: column;
        justify-content: center;
        color: var(--iq-dark);
        text-align: center;

        i {
            font-size: 60px;
        }

        h5 {
            color: var(--iq-dark) !important;
        }
    }

    .color-inherit {
        color: inherit !important;
    }
</style>
