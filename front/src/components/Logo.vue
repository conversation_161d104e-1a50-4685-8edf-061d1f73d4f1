<template>
    <router-link :to="{name: 'homepage'}" class="header-logo d-inline-block mx-auto">
        <span :class="`logo-title logo-title-left font-weight-bold align-middle ${light ? 'logo-light' : ''}`">Li</span>
        <img :src="light ? logoLight : logo" class="img-fluid rounded-normal" alt="logo">
        <span :class="`logo-title font-weight-bold align-middle ${light ? 'logo-light' : ''}`">erom</span>
    </router-link>
</template>

<script>
    import logo from '../assets/images/logo.png';
    import logoLight from '../assets/images/logo_light.png';

    export default {
        props: {
            light: Boolean,
        },
        mounted: function () {
        },
        data: function () {
            return {
                logo: logo,
                logoLight: logoLight,
            };
        },
        computed: {},
        methods: {},
        watch: {},
    };
</script>

<style lang="scss">
    .header-logo {
        img {
            width: 27px;
        }
    }

    .logo-title {
        margin-left: 1px;
        font-size: 32px;
        letter-spacing: -1px;
        color: var(--iq-dark-body-bg);

        &.logo-light {
            color: var(--iq-dark-body-text);
        }

        &.logo-title-left {
            margin-right: 4px;
        }
    }
</style>
