<template>
    <div class="iq-sidebar" id="iq-sidebar">
        <div class="d-flex justify-content-between pt-3">
            <Logo :light="darkMode"></Logo>
            <div class="iq-menu-bt-sidebar" v-if="toggleButton">
                <div class="iq-menu-bt align-self-center">
                    <div class="wrapper-menu" @click="miniSidebar">
                        <div class="main-circle"><i class="las la-bars"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <div id="sidebar-scrollbar">
            <nav class="iq-sidebar-menu">
                <CollapseMenu :items="items" :open="true" :horizontal="horizontal" :sidebarGroupTitle="sidebarGroupTitle"/>
            </nav>
        </div>
    </div>
</template>

<script>
    import CollapseMenu from '../menus/CollapseMenu';
    import Logo from '../../Logo';
    import {mapGetters} from 'vuex';

    export default {
        name: 'Sidebar',
        props: {
            items: {type: Array},
            horizontal: {type: Boolean},
            toggleButton: {
                type: Boolean,
                default: true,
            },
            sidebarGroupTitle: {
                type: Boolean,
                default: true,
            },
        },
        components: {
            CollapseMenu,
            Logo,
        },
        mounted: function() {
            if (document.body.classList.contains('sidebar-main')) {
                this.miniSidebar();
            }

            window.addEventListener('click', e => {
                if (this.miniSidebarState
                    && !document.getElementById('iq-sidebar').contains(e.target)
                    && !document.getElementById('iq-menu-bt').contains(e.target)) {
                    this.miniSidebar();
                }
            });
        },
        data: function() {
            return {};
        },
        computed: {
            ...mapGetters({
                darkMode: 'Setting/darkModeState',
                miniSidebarState: 'Setting/miniSidebarState',
            }),
        },
        methods: {
            miniSidebar: function () {
                this.$emit('toggle');
            },
        },
    };
</script>

<style scoped lang="scss">
    .iq-menu-bt-sidebar {
        @media (min-width: 1300px) {
            display: none;
        }
    }
</style>
