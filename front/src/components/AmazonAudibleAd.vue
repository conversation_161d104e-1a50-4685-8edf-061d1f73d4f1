<template>
    <div class="alignleft">
        <div id="adVariables"></div>
        <div id="adScript"></div>
    </div>
</template>

<script>
    import postscribe from 'postscribe';
    import {Capacitor} from '@capacitor/core';
    import HttpClient from '../services/HttpClient';
    import settings from '../config/settings';

    export default {
        props: {
            countryCode: String,
        },
        components: {},
        mounted: async function () {
            return;

            let adsTrackingId = settings.adsTrackingIds[this.countryCode];
            if (adsTrackingId === undefined) {
                adsTrackingId = settings.adsTrackingIds.international;
            }

            let platform = Capacitor.isNativePlatform() ? 'android' : 'web';
            adsTrackingId = adsTrackingId[platform];

            postscribe('#adVariables', `<script type="text/javascript">
                amzn_assoc_ad_type = "banner";
                amzn_assoc_marketplace = "amazon";
                amzn_assoc_region = "US";
                amzn_assoc_placement = "assoc_banner_placement_default";
                amzn_assoc_campaigns = "audibleplus";
                amzn_assoc_banner_type = "promotions";
                amzn_assoc_p = "12";
                amzn_assoc_banner_id = "0MG2XKQ7PYPP84NBNFR2";
                amzn_assoc_width = "300";
                amzn_assoc_height = "250";
                amzn_assoc_tracking_id = "${adsTrackingId}";
                amzn_assoc_linkid = "3be897871b796a734e4616b16030b21b";
            <\/script>`);

            let response = await HttpClient.get('https://z-na.amazon-adsystem.com/widgets/q?ServiceVersion=20070822&Operation=GetScript&ID=OneJS&WS=1', {}, {
                isExternal: true,
            });
            if (response.status === 200) {
                // these fixes are needed for the script to work on android
                let amazonScript = response.data.replaceAll("'//", "'https://");
                amazonScript = amazonScript.replaceAll('"//', '"https://');
                amazonScript = amazonScript.replaceAll('http://', 'https://');

                postscribe('#adScript', `<script type="text/javascript">${amazonScript}<\/script>`);
            }
        },
        data: function () {
            return {};
        },
        methods: {},
        watch: {},
    };
</script>

<style lang="scss">

</style>
