<template>
    <div>
        <Loader/>
        <div class="wrapper">
            <!-- Sidebar  -->
            <Sidebar :items="verticalMenu" @toggle="sidebarMini" :toggleButton="toggleSideIcon" :sidebarGroupTitle="sidebarGroupTitle"/>
            <!-- TOP Nav Bar -->
            <DefaultNavBar title="Dashboard" :homeURL="{ name: 'shop.home' }" :sidebarGroupTitle="sidebarGroupTitle" @toggle="sidebarMini">
                <template slot="responsiveRight">
                    <ul class="navbar-nav ml-auto navbar-list">
                        <li class="nav-item nav-icon search-content">
                            <a href="#" class="search-toggle iq-waves-effect text-gray rounded">
                                <i class="ri-search-line"></i>
                            </a>
                            <div class="search-box p-0">
                                <input type="text" class="text search-input w-75" :placeholder="$t('header.searchPlaceholder')" v-model="searchInput" @keydown.enter="goToSearch">
                                <span class="search-link" href="#"><i class="ri-search-line"></i></span>
                                <button type="submit" class="btn btn-primary search-data w-25" @click="goToSearch">{{ $t('header.search') }}</button>
                            </div>
                        </li>
<!--                        <li class="nav-item nav-icon">-->
<!--                            <a href="#" class="search-toggle iq-waves-effect text-gray rounded">-->
<!--                                <i class="ri-notification-2-line"></i>-->
<!--                                <span class="bg-primary dots"></span>-->
<!--                            </a>-->
<!--                            <div class="iq-sub-dropdown">-->
<!--                                <div class="iq-card shadow-none m-0">-->
<!--                                    <div class="iq-card-body p-0">-->
<!--                                        <div class="bg-primary p-3">-->
<!--                                            <h5 class="mb-0 text-white">All Notifications<small-->
<!--                                                class="badge  badge-light float-right pt-1">4</small></h5>-->
<!--                                        </div>-->
<!--                                        <a href="#" class="iq-sub-card">-->
<!--                                            <div class="media align-items-center">-->
<!--                                                <div class="">-->
<!--                                                    <img class="avatar-40 rounded" src="../assets/images/user/01.jpg"-->
<!--                                                        alt="">-->
<!--                                                </div>-->
<!--                                                <div class="media-body ml-3">-->
<!--                                                    <h6 class="mb-0 ">Emma Watson Barry</h6>-->
<!--                                                    <small class="float-right font-size-12">Just Now</small>-->
<!--                                                    <p class="mb-0">95 MB</p>-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </a>-->
<!--                                        <a href="#" class="iq-sub-card">-->
<!--                                            <div class="media align-items-center">-->
<!--                                                <div class="">-->
<!--                                                    <img class="avatar-40 rounded" src="../assets/images/user/02.jpg"-->
<!--                                                        alt="">-->
<!--                                                </div>-->
<!--                                                <div class="media-body ml-3">-->
<!--                                                    <h6 class="mb-0 ">New customer is join</h6>-->
<!--                                                    <small class="float-right font-size-12">5 days ago</small>-->
<!--                                                    <p class="mb-0">Cyst Barry</p>-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </a>-->
<!--                                        <a href="#" class="iq-sub-card">-->
<!--                                            <div class="media align-items-center">-->
<!--                                                <div class="">-->
<!--                                                    <img class="avatar-40 rounded" src="../assets/images/user/03.jpg"-->
<!--                                                        alt="">-->
<!--                                                </div>-->
<!--                                                <div class="media-body ml-3">-->
<!--                                                    <h6 class="mb-0 ">Two customer is left</h6>-->
<!--                                                    <small class="float-right font-size-12">2 days ago</small>-->
<!--                                                    <p class="mb-0">Cyst Barry</p>-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </a>-->
<!--                                        <a href="#" class="iq-sub-card">-->
<!--                                            <div class="media align-items-center">-->
<!--                                                <div class="">-->
<!--                                                    <img class="avatar-40 rounded" src="../assets/images/user/04.jpg"-->
<!--                                                        alt="">-->
<!--                                                </div>-->
<!--                                                <div class="media-body ml-3">-->
<!--                                                    <h6 class="mb-0 ">New Mail from Fenny</h6>-->
<!--                                                    <small class="float-right font-size-12">3 days ago</small>-->
<!--                                                    <p class="mb-0">Cyst Barry</p>-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </a>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </li>-->
<!--                        <li class="nav-item nav-icon dropdown">-->
<!--                            <a href="#" class="search-toggle iq-waves-effect text-gray rounded">-->
<!--                                <i class="ri-mail-line" aria-hidden="true"></i>-->
<!--                                <span class="bg-primary count-mail"></span>-->
<!--                            </a>-->
<!--                            <div class="iq-sub-dropdown">-->
<!--                                <div class="iq-card shadow-none m-0">-->
<!--                                    <div class="iq-card-body p-0 ">-->
<!--                                        <div class="bg-primary p-3">-->
<!--                                            <h5 class="mb-0 text-white">All Messages<small-->
<!--                                                class="badge  badge-light float-right pt-1">5</small></h5>-->
<!--                                        </div>-->
<!--                                        <a href="#" class="iq-sub-card">-->
<!--                                            <div class="media align-items-center">-->
<!--                                                <div class="">-->
<!--                                                    <img class="avatar-40 rounded" src="../assets/images/user/01.jpg"-->
<!--                                                        alt="">-->
<!--                                                </div>-->
<!--                                                <div class="media-body ml-3">-->
<!--                                                    <h6 class="mb-0 ">Barry Emma Watson</h6>-->
<!--                                                    <small class="float-left font-size-12">13 Jun</small>-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </a>-->
<!--                                        <a href="#" class="iq-sub-card">-->
<!--                                            <div class="media align-items-center">-->
<!--                                                <div class="">-->
<!--                                                    <img class="avatar-40 rounded" src="../assets/images/user/02.jpg"-->
<!--                                                        alt="">-->
<!--                                                </div>-->
<!--                                                <div class="media-body ml-3">-->
<!--                                                    <h6 class="mb-0 ">Lorem Ipsum Watson</h6>-->
<!--                                                    <small class="float-left font-size-12">20 Apr</small>-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </a>-->
<!--                                        <a href="#" class="iq-sub-card">-->
<!--                                            <div class="media align-items-center">-->
<!--                                                <div class="">-->
<!--                                                    <img class="avatar-40 rounded" src="../assets/images/user/03.jpg"-->
<!--                                                        alt="">-->
<!--                                                </div>-->
<!--                                                <div class="media-body ml-3">-->
<!--                                                    <h6 class="mb-0 ">Why do we use it?</h6>-->
<!--                                                    <small class="float-left font-size-12">30 Jun</small>-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </a>-->
<!--                                        <a href="#" class="iq-sub-card">-->
<!--                                            <div class="media align-items-center">-->
<!--                                                <div class="">-->
<!--                                                    <img class="avatar-40 rounded" src="../assets/images/user/04.jpg"-->
<!--                                                        alt="">-->
<!--                                                </div>-->
<!--                                                <div class="media-body ml-3">-->
<!--                                                    <h6 class="mb-0 ">Variations Passages</h6>-->
<!--                                                    <small class="float-left font-size-12">12 Sep</small>-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </a>-->
<!--                                        <a href="#" class="iq-sub-card">-->
<!--                                            <div class="media align-items-center">-->
<!--                                                <div class="">-->
<!--                                                    <img class="avatar-40 rounded" src="../assets/images/user/05.jpg"-->
<!--                                                        alt="">-->
<!--                                                </div>-->
<!--                                                <div class="media-body ml-3">-->
<!--                                                    <h6 class="mb-0 ">Lorem Ipsum generators</h6>-->
<!--                                                    <small class="float-left font-size-12">5 Dec</small>-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </a>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </li>-->
<!--                        <li class="nav-item nav-icon dropdown">-->
<!--                            <a href="#" class="search-toggle iq-waves-effect text-gray rounded">-->
<!--                                <i class="ri-shopping-cart-2-line"></i>-->
<!--                                <span class="badge badge-danger count-cart rounded-circle">4</span>-->
<!--                            </a>-->
<!--                            <div class="iq-sub-dropdown">-->
<!--                                <div class="iq-card shadow-none m-0">-->
<!--                                    <div class="iq-card-body p-0 toggle-cart-info">-->
<!--                                        <div class="bg-primary p-3">-->
<!--                                            <h5 class="mb-0 text-white">All Carts<small-->
<!--                                                class="badge  badge-light float-right pt-1">4</small></h5>-->
<!--                                        </div>-->
<!--                                        <a href="#" class="iq-sub-card">-->
<!--                                            <div class="media align-items-center">-->
<!--                                                <div class="">-->
<!--                                                    <img class="rounded" src="../assets/images/cart/01.jpg" alt="">-->
<!--                                                </div>-->
<!--                                                <div class="media-body ml-3">-->
<!--                                                    <h6 class="mb-0 ">Night People book</h6>-->
<!--                                                    <p class="mb-0">$32</p>-->
<!--                                                </div>-->
<!--                                                <div class="float-right font-size-24 text-danger"><i-->
<!--                                                    class="ri-close-fill"></i></div>-->
<!--                                            </div>-->
<!--                                        </a>-->
<!--                                        <a href="#" class="iq-sub-card">-->
<!--                                            <div class="media align-items-center">-->
<!--                                                <div class="">-->
<!--                                                    <img class="rounded" src="../assets/images/cart/02.jpg" alt="">-->
<!--                                                </div>-->
<!--                                                <div class="media-body ml-3">-->
<!--                                                    <h6 class="mb-0 ">The Sin Eater Book</h6>-->
<!--                                                    <p class="mb-0">$40</p>-->
<!--                                                </div>-->
<!--                                                <div class="float-right font-size-24 text-danger"><i-->
<!--                                                    class="ri-close-fill"></i></div>-->
<!--                                            </div>-->
<!--                                        </a>-->
<!--                                        <a href="#" class="iq-sub-card">-->
<!--                                            <div class="media align-items-center">-->
<!--                                                <div class="">-->
<!--                                                    <img class="rounded" src="../assets/images/cart/03.jpg" alt="">-->
<!--                                                </div>-->
<!--                                                <div class="media-body ml-3">-->
<!--                                                    <h6 class="mb-0 ">the Orange Tree</h6>-->
<!--                                                    <p class="mb-0">$30</p>-->
<!--                                                </div>-->
<!--                                                <div class="float-right font-size-24 text-danger"><i-->
<!--                                                    class="ri-close-fill"></i></div>-->
<!--                                            </div>-->
<!--                                        </a>-->
<!--                                        <a href="#" class="iq-sub-card">-->
<!--                                            <div class="media align-items-center">-->
<!--                                                <div class="">-->
<!--                                                    <img class="rounded" src="../assets/images/cart/04.jpg" alt="">-->
<!--                                                </div>-->
<!--                                                <div class="media-body ml-3">-->
<!--                                                    <h6 class="mb-0 ">Harsh Reality book</h6>-->
<!--                                                    <p class="mb-0">$25</p>-->
<!--                                                </div>-->
<!--                                                <div class="float-right font-size-24 text-danger"><i-->
<!--                                                    class="ri-close-fill"></i></div>-->
<!--                                            </div>-->
<!--                                        </a>-->
<!--                                        <div class="d-flex align-items-center text-center p-3">-->
<!--                                            <a class="btn btn-primary mr-2 iq-sign-btn text-white" href="#"-->
<!--                                                role="button">View Cart</a>-->
<!--                                            <a class="btn btn-primary iq-sign-btn text-white" href="#" role="button">Shop-->
<!--                                                now</a>-->
<!--                                        </div>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </li>-->
                        <li class="line-height pt-3">
                            <a href="#" class="search-toggle iq-waves-effect d-flex align-items-center">
                                <ProfilePicture :url="authUser.profilePicture" className="profile-picture mr-3 text-center fa-2-8x" />
                                <div class="caption">
                                    <h6 class="mb-1 line-height">{{ authUser.name }}</h6>
                                </div>
                            </a>
                            <div class="iq-sub-dropdown iq-user-dropdown">
                                <div class="iq-card shadow-none m-0">
                                    <div class="iq-card-body p-0 ">
                                        <div class="bg-primary p-3">
                                            <h5 class="mb-0 text-white line-height">{{ $t('header.hello') }} {{ authUser.name }}</h5>
                                        </div>
                                        <router-link to="/account-settings" class="iq-sub-card iq-bg-primary-hover">
                                            <div class="media align-items-center">
                                                <div class="rounded iq-card-icon iq-bg-primary">
                                                    <i class="ri-account-box-line"></i>
                                                </div>
                                                <div class="media-body ml-3">
                                                    <h6 class="mb-0 lh-1-6x">{{ $t('header.accountSettings') }}</h6>
                                                </div>
                                            </div>
                                        </router-link>
                                        <div class="iq-sub-card">
                                            <div class="media align-items-center">
                                                <div class="rounded iq-card-icon iq-bg-primary">
                                                    <i class="las la-language"></i>
                                                </div>
                                                <div class="media-body ml-3">
                                                    <b-form>
                                                        <LanguageSelect />
                                                    </b-form>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="iq-sub-card">
                                            <b-form-checkbox class="dark-mode-checkbox w-100" color="dark"
                                                v-model="darkModeCheckbox" name="check-button" switch inline>
                                                <h6 class="mb-0 lh-1-6x">{{ $t('header.darkTheme') }}</h6>
                                            </b-form-checkbox>
                                        </div>
<!--                                        <router-link to="/privacy-setting" class="iq-sub-card iq-bg-primary-hover">-->
<!--                                            <div class="media align-items-center">-->
<!--                                                <div class="rounded iq-card-icon iq-bg-primary">-->
<!--                                                    <i class="ri-lock-line"></i>-->
<!--                                                </div>-->
<!--                                                <div class="media-body ml-3">-->
<!--                                                    <h6 class="mb-0 ">Privacy Settings</h6>-->
<!--                                                    <p class="mb-0 font-size-12">Control your privacy parameters.</p>-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </router-link>-->
                                        <div v-if="isAuthenticated" class="d-inline-block w-100 text-center p-3">
                                            <button type="button" class="bg-primary iq-sign-btn" @click="this.logout">
                                                {{ $t('header.logout') }}<i class="ri-login-box-line ml-2"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </template>
            </DefaultNavBar>
            <!-- TOP Nav Bar END -->
            <div id="content-page" class="content-page">
                <transition name="router-anim" :enter-active-class="`animated fastest ${animated.enter}`" mode="out-in"
                    :leave-active-class="`animated fastest ${animated.exit}`">
                    <router-view :key="$route.fullPath"/>
                </transition>
            </div>
        </div>
        <LayoutFooter/>
    </div>
</template>

<script>
    import {core} from '../config/pluginInit';
    import {mapActions, mapGetters} from 'vuex';
    import Loader from '../components/core/loader/Loader';
    import Sidebar from '../components/core/sidebars/Sidebar';
    import DefaultNavBar from '../components/core/navbars/DefaultNavBar';
    import LayoutFooter from './Components/LayoutFooter';
    import Auth from '../services/Auth';
    import ProfilePicture from '../components/ProfilePicture';
    import LanguageSelect from '../components/LanguageSelect';
    import settings from '../config/settings';

    export default {
        components: {
            LayoutFooter,
            Loader,
            Sidebar,
            DefaultNavBar,
            ProfilePicture,
            LanguageSelect,
        },
        mounted()
        {
            this.modeChange({
                rtl: false,
                dark: this.darkModeCheckbox,
            });

            this.layoutSetting(this.$route.name);
            this.isAuthenticated = Auth.isAuthenticated();
        },
        computed: {
            ...mapGetters({
                darkMode: 'Setting/darkModeState',
                colors: 'Setting/colorState',
                authUser: 'Setting/authUserState',
            }),
            toggleSideIcon()
            {
                let show = true;
                switch (this.$route.name) {
                    case 'dashboard.home-1':
                    case 'dashboard.home-3':
                    case 'dashboard.home-4':
                    case 'dashboard.home-5':
                    case 'dashboard.home-6':
                        show = false;
                        break;
                }
                return show;
            },
        },
        watch: {
            '$route': function (to, from) {
                this.layoutSetting(to.name);
            },
            darkModeCheckbox: function () {
                this.modeChange({
                    rtl: false,
                    dark: this.darkModeCheckbox,
                });
                window.localStorage.setItem('darkMode', this.darkModeCheckbox ? '1' : '0');
            },
        },
        data()
        {
            let darkMode = window.localStorage.getItem('darkMode');

            return {
                searchInput: '',
                animated: {
                    enter: 'fadeIn',
                    exit: 'fadeOut',
                },
                verticalMenu: settings.menu,
                userProfile: null,
                isAuthenticated: false,
                darkModeCheckbox: darkMode === null || darkMode === '1',
                sidebarGroupTitle: true,
                rtl: false,
                message: [
                    {
                        name: 'Nik Emma Watson',
                        date: '13 jan',
                    },
                    {
                        name: 'Greta Life',
                        date: '14 Jun',
                    },
                    {
                        name: 'Barb Ackue',
                        date: '16 Aug',
                    },
                    {
                        name: 'Anna Sthesia',
                        date: '21 Sept',
                    },
                    {
                        name: 'Bob Frapples',
                        date: '29 Sept',
                    },
                ],
                notification: [
                    {
                        name: 'Nik Emma Watson',
                        date: '23 hour ago',
                        description: 'Enjoy smart access to videos, games',
                    },
                    {
                        name: 'Greta Life',
                        date: '14 hour ago',
                        description: 'Google Chromecast: Enjoy a world of entertainment',
                    },
                    {
                        name: 'Barb Ackue',
                        date: '16 hour ago',
                        description: 'Dell Inspiron Laptop: Get speed and performance from',
                    },
                    {
                        name: 'Anna Sthesia',
                        date: '21 hour ago',
                        description: 'Deliver your favorite playlist anywhere in your home ',
                    },
                    {
                        name: 'Bob Frapples',
                        date: '11 hour ago',
                        description: 'MacBook Air features up to 8GB of memory, a fifth-generation',
                    },
                ],
            };
        },
        methods: {
            layoutSetting(routeName)
            {
                this.sidebarGroupTitle = true;
                switch (routeName) {
                    case 'dashboard.home-5':
                        break;
                    case 'dashboard.home-6':
                        this.sidebarGroupTitle = false;
                        break;
                }
            },
            sidebarMini()
            {
                core.triggerSet();
                this.$store.dispatch('Setting/miniSidebarAction');
            },
            async logout()
            {
                let response = await Auth.logout();
                if (response.status === 200) {
                    await this.$router.push({name: 'register'});
                }
            },
            routerAnimationChange(e)
            {
                this.animated = e;
            },
            goToSearch()
            {
                this.$router.push({
                    path: '/search',
                    query: {title: this.searchInput},
                });
            },
            ...mapActions({
                rtlAdd: 'Setting/setRtlAction',
                rtlRemove: 'Setting/removeRtlAction',
                modeChange: 'Setting/layoutModeAction',
            }),
        },
    };
</script>

<style lang="scss">
    @import url("../assets/css/custom.css");
    @import url("../assets/css/PriceSlider.css");

    .animated.fastest {
        -webkit-animation-duration: 200ms;
        animation-duration: 200ms;
    }

    .dark-mode-checkbox {
        padding-left: 45px !important;

        .custom-control-label {
            width: 100%;
            padding-left: 1rem;
            cursor: pointer;
        }
    }

    .profile-picture {
        width: 50px;
        height: 50px !important;
    }

    .fa-2-8x {
        font-size: 2.8em;
    }

    .lh-1-6x {
        line-height: 1.6em !important;
    }
</style>
