<template>
    <div>
        <MiniFooter>
            <template v-slot:left>
                <li class="list-inline-item">
                    <router-link to="/privacy-policy">Privacy policy</router-link>
                </li>
                <li class="list-inline-item">
                    <router-link to="/terms-of-service">Terms of use</router-link>
                </li>
            </template>
            <template v-slot:center>
                <a href="https://instagram.com/liberomapp/" class="mr-4" target="_blank">
                    <i aria-hidden="true" class="fab fa-instagram fa-instagram-f fa-instagram-p footer-social align-middle"></i>
                </a>
                <a href="https://twitter.com/liberomapp" class="mr-4" target="_blank">
                    <i aria-hidden="true" class="fab fa-twitter fa-twitter-f fa-twitter-p footer-social align-middle"></i>
                </a>
                <a href="https://facebook.com/liberom.ap" class="mr-4" target="_blank">
                    <i aria-hidden="true" class="fab fa-facebook fa-facebook-f fa-facebook-p footer-social align-middle"></i>
                </a>
                <a href="https://ko-fi.com/liberom" class="align-middle" target="_blank">
                    <img :src="koFi" alt="Ko-fi" class="ko-fi">
                </a>
            </template>
            <template v-slot:right>
                Copyright {{ year }}
                <router-link to="/">{{ appName }}</router-link>
                All Rights Reserved.
            </template>
        </MiniFooter>
    </div>
</template>

<script>
    import koFi from '../../assets/images/ko-fi.png';
    import MiniFooter from '../../components/core/footer/MiniFooter';
    import settings from "@/config/settings";

    export default {
        name: 'LayoutFooter',
        components: {
            MiniFooter,
        },
        mounted: function () {
            this.year = new Date().getFullYear();
        },
        data: function() {
            return {
                year: '',
                koFi: koFi,
                appName: settings.name
            };
        },
    };
</script>

<style>
    .ko-fi {
        width: 150px;
    }

    .footer-social {
        font-size: 20px;
    }

    [mode="dark"] {
        .footer-social {
            color: var(--iq-dark-title-text);
        }
    }

    [mode="light"] {
        .footer-social {
            color: var(--iq-dark);
        }
    }
</style>
