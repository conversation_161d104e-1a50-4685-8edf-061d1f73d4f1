<template>
    <b-container fluid>
        <b-row>
            <b-col lg="12">
                <iq-card className="iq-card-transparent mb-0">
                    <div class="d-block">
                        <div class="w-100 iq-search-filter">
                            <ul class="list-inline p-0 m-0 row search-menu-options">
                                <li class="search-menu-opt">
                                    <div :class="'iq-search-bar search-book' + (tutorialPopovers.searchForm ? ' tutorial-border' : '')" id="search-form">
                                        <div class="searchbox">
                                            <input type="text" v-model="filters.title" @keydown.enter="setQueryFromFilters" class="text search-input pl-3 mb-3" :placeholder="$t('search.title')">
                                            <vue-multiselect type="text" v-model="filters.authors" @keydown.enter="setQueryFromFilters" class="text mb-3" :placeholder="$t('search.author')"
                                                :options="authorOptions" :multiple="true" :hide-selected="true" :showNoResults="false" :showNoOptions="false" :loading="isAuthorsLoading" @search-change="getAuthors">
                                            </vue-multiselect>
                                            <vue-multiselect v-model="filters.subjects" @keydown.enter="setQueryFromFilters" class="text mb-3" :placeholder="$t('search.subject')"
                                                :options="subjectOptions" :multiple="true" :hide-selected="true" :showNoResults="false" :showNoOptions="false" :loading="isSubjectsLoading" @search-change="getSubjects">
                                            </vue-multiselect>
                                            <input type="text" v-model="filters.isbn" @keydown.enter="setQueryFromFilters" class="text search-input pl-3 mb-3" :placeholder="$t('search.isbn')">
                                            <input type="number" v-model="filters.year_from" @keydown.enter="setQueryFromFilters" class="text search-input pl-3 mb-3 w-48" :placeholder="$t('search.minPublishYear')">
                                            -
                                            <input type="number" v-model="filters.year_to" @keydown.enter="setQueryFromFilters" class="text search-input pl-3 mb-3 w-48" :placeholder="$t('search.maxPublishYear')">
                                            <b-form-select plain v-model="filters.language" :options="languages" @keydown.enter="setQueryFromFilters" class="text search-input pl-3 mb-3">
                                                <template #first>
                                                    <b-form-select-option :value="null">{{ $t('search.language') }}</b-form-select-option>
                                                </template>
                                            </b-form-select>
                                            <b-form-checkbox v-model="filters.showFree" name="showFree" switch inline>
                                                {{ $t('search.freeBooks') }}
                                            </b-form-checkbox>
                                            <button @click="setQueryFromFilters" class="btn btn-primary btn-lg d-block mt-3 search-data">{{ $t('search.search') }}</button>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </iq-card>
                <iq-card id="search-results" ref="searchResults" :class="tutorialPopovers.selectBook ? 'tutorial-border' : ''">
                    <template v-slot:body>
                        <BookListSmall :books="books"></BookListSmall>
                    </template>
                </iq-card>
                <div class="middle-screen" id="middle-screen"></div>
                <b-popover target="middle-screen" triggers="manual" :show.sync="tutorialPopovers.getStarted" placement="top" :title="$t('tutorial.welcomeTitle')" :key="componentRefreshHack">
                    {{ $t('tutorial.welcome') }}
                    <button @click="goToTutorialSearchFormStep" class="btn btn-primary btn-lg d-block btn-align-right mt-1">{{ $t('tutorial.welcomeButton') }}</button>
                </b-popover>
                <b-popover target="search-form" triggers="manual" :show.sync="tutorialPopovers.searchForm" placement="right" :title="$t('search.search')" :key="componentRefreshHack + 1">
                    {{ $t('tutorial.searchForm') }}
                    <button @click="goToTutorialSelectBookStep" class="btn btn-primary btn-lg d-block btn-align-right mt-1">{{ $t('tutorial.nextButton') }}</button>
                </b-popover>
                <b-popover target="search-results" triggers="manual" :show.sync="tutorialPopovers.selectBook" placement="top" :title="$t('book.goToBook')" :key="componentRefreshHack + 2">
                    {{ $t('tutorial.selectBook') }}
                </b-popover>
            </b-col>
        </b-row>
    </b-container>
</template>

<script>
    import {core} from '../config/pluginInit';
    import settings from '../config/settings';
    import HttpClient from '../services/HttpClient';
    import BookListSmall from '../components/BookListSmall';
    import VueMultiselect from 'vue-multiselect'
    import {mapActions, mapGetters} from 'vuex';

    export default {
        components: {
            BookListSmall,
            VueMultiselect,
        },
        created: function () {},
        mounted: function () {
            core.index();
            this.getLanguages();
            this.resetFilters();
            this.setFiltersFromQuery();
            this.addOnScroll();
            this.abortController = new AbortController();
            this.handleTutorial();
        },
        destroyed: function () {
            this.removeOnCall();
        },
        data: function () {
            return {
                filters: {},
                books: [],
                paidBooks: settings.search.paidBooks,
                freeBooks: settings.search.freeBooks,
                scrollEndPixels: 1000,
                isScrollLoading: false,
                languages: [],
                subjectOptions: [],
                authorOptions: [],
                isSubjectsLoading: false,
                isAuthorsLoading: false,
                subjectsTimeout: null,
                authorsTimeout: null,
                abortController: null,
                tutorialPopovers: {
                    getStarted: false,
                    searchForm: false,
                    selectBook: false,
                },
                componentRefreshHack: 0,
            };
        },
        computed: {
            ...mapGetters({
                authUser: 'Setting/authUserState',
                tutorialStep: 'Setting/tutorialStepState',
            }),
        },
        methods: {
            getLanguages: async function () {
                let response = await HttpClient.get('/languages');
                if (response.status === 200) {
                    this.languages = response.data;
                }
            },
            getSubjects: async function (keyword) {
                if (this.subjectsTimeout) {
                    clearTimeout(this.subjectsTimeout);
                }

                this.subjectsTimeout = setTimeout(async () => {
                    if (keyword === '') {
                        return;
                    }

                    this.isSubjectsLoading = true;

                    this.abortController.abort();
                    this.abortController = new AbortController();

                    let headers = {};
                    if (settings.searchApiKey !== '') {
                        headers = {
                            headers: {
                                Authorization: `Bearer ${settings.searchApiKey}`,
                            },
                        };
                    }

                    let response = await HttpClient.post(settings.searchSubjectsUrl, {
                        q: keyword,
                        limit: 50,
                    }, {
                        signal: this.abortController.signal,
                        ...headers,
                    });

                    if (response !== undefined && response.status === 200) {
                        this.subjectOptions = response.data.hits.map(subject => subject.name);
                        this.isSubjectsLoading = false;
                    }
                }, 800)
            },
            getAuthors: <AUTHORS>
                if (this.authorsTimeout) {
                    clearTimeout(this.authorsTimeout);
                }

                this.authorsTimeout = setTimeout(async () => {
                    if (keyword === '') {
                        return;
                    }

                    this.isAuthorsLoading = true;

                    this.abortController.abort();
                    this.abortController = new AbortController();

                    let headers = {};
                    if (settings.searchApiKey !== '') {
                        headers = {
                            headers: {
                                Authorization: `Bearer ${settings.searchApiKey}`,
                            },
                        };
                    }

                    let response = await HttpClient.post(settings.searchAuthorsUrl, {
                        q: keyword,
                        limit: 50,
                    }, {
                        signal: this.abortController.signal,
                        ...headers,
                    });

                    if (response !== undefined && response.status === 200) {
                        this.authorOptions = response.data.hits.map(author => author.name);
                        this.isAuthorsLoading = false;
                    }
                }, 800)
            },
            setFiltersFromQuery: function (queryArgument) {
                this.resetFilters();

                let queryParams = queryArgument === undefined ? this.$route.query : queryArgument;
                queryParams = Object.entries(queryParams);
                if (queryParams.length === 0) {
                    queryParams = [['showFree', true]];
                }

                let doSearch = false;
                for (let [query, value] of queryParams) {
                    if (this.filters[query] !== undefined) {
                        if (value === 'false') {
                            value = false;
                        }

                        this.filters[query] = value;
                        doSearch = true;
                    }
                }

                if (doSearch) {
                    this.getBooks();
                }
            },
            setQueryFromFilters: function () {
                let query = {};
                for (let filterName in this.filters) {
                    if (filterName === 'offset' || filterName === 'limit' || this.filters[filterName] === '' || this.filters[filterName] === null) {
                        continue;
                    }

                    query[filterName] = this.filters[filterName];
                }

                if (Object.keys(query).length > 0) {
                    let {href} = this.$router.resolve({
                        name: 'search',
                        query: query,
                    });
                    window.history.pushState({}, null, href);

                    this.setFiltersFromQuery(query);
                }
            },
            getBooks: async function () {
                if (this.isEmptySearch()) {
                    this.books = this.filters.showFree ? this.freeBooks : this.paidBooks;

                    return;
                }

                this.filters.offset = 0;
                this.isScrollLoading = false;

                let filters = {...this.filters};
                if (filters.language === null) {
                    filters.language = '';
                }

                let searchAttributes = this.mapFiltersToSearch(filters);

                let headers = {};
                if (settings.searchApiKey !== '') {
                    headers = {
                        headers: {
                            Authorization: `Bearer ${settings.searchApiKey}`,
                        },
                    };
                }

                let response = await HttpClient.post(settings.searchUrl, searchAttributes, {
                    ...headers,
                });
                if (response.status === 200) {
                    this.books = response.data.hits;
                    this.filters.offset += this.filters.limit;
                }
            },
            mapFiltersToSearch: function (filters) {
                let searchAttributes = {};
                if (filters.title !== '') {
                    searchAttributes.q = filters.title;
                }

                let searchFilters = [];
                if (filters.authors.length > 0) {
                    if (!Array.isArray(filters.authors)) {
                        filters.authors = [filters.authors];
                    }

                    let authorsFilter = [];
                    filters.authors.forEach(author => {
                        authorsFilter.push(`authors = "${author}"`);
                    });

                    searchFilters.push(authorsFilter.join(' AND '));
                }
                if (filters.isbn !== '') {
                    searchFilters.push(`identifiers.ISBN13 = "${filters.isbn}"
                        OR identifiers.isbn13 = "${filters.isbn}"
                        OR identifiers.ISBN10 = "${filters.isbn}"
                        OR identifiers.isbn10 = "${filters.isbn}"
                        OR identifiers.ISBN = "${filters.isbn}"
                        OR identifiers.isbn = "${filters.isbn}"`);
                }
                if (filters.language !== '') {
                    searchFilters.push(`languages.value = "${filters.language}"`);
                }
                if (filters.showFree === true || filters.showFree === 'true') {
                    searchFilters.push('isFree = 1');
                }
                if (filters.subjects.length > 0) {
                    if (!Array.isArray(filters.subjects)) {
                        filters.subjects = [filters.subjects];
                    }

                    let subjectsFilter = [];
                    filters.subjects.forEach(subject => {
                        subjectsFilter.push(`subjects = "${subject}"`);
                    });

                    searchFilters.push(subjectsFilter.join(' AND '));
                }
                if (filters.year_from !== '') {
                    searchFilters.push(`publishDates >= ${filters.year_from}`);
                }
                if (filters.year_to !== '') {
                    searchFilters.push(`publishDates <= ${filters.year_to}`);
                }

                if (searchFilters.length > 0) {
                    searchAttributes.filter = searchFilters;
                }

                searchAttributes.offset = filters.offset;
                searchAttributes.limit = filters.limit;

                return searchAttributes;
            },
            addOnScroll: async function () {
                window.onscroll = async () => {
                    if (this.isEmptySearch() || this.isScrollLoading) {
                        return;
                    }

                    if (document.documentElement.scrollTop + document.documentElement.clientHeight
                        < document.documentElement.scrollHeight - this.scrollEndPixels) {
                        return;
                    }

                    this.isScrollLoading = true;

                    let filters = {...this.filters};
                    if (filters.language === null) {
                        filters.language = '';
                    }

                    let searchAttributes = this.mapFiltersToSearch(filters);

                    let headers = {};
                    if (settings.searchApiKey !== '') {
                        headers = {
                            headers: {
                                Authorization: `Bearer ${settings.searchApiKey}`,
                            },
                        };
                    }

                    let response = await HttpClient.post(settings.searchUrl, searchAttributes, {
                        ...headers,
                    });

                    if (response.status === 200) {
                        this.books = this.books.concat(response.data.hits);
                        this.filters.offset += this.filters.limit;

                        if (response.data.hits.length > 0) {
                            this.isScrollLoading = false;
                        }
                    }
                }
            },
            removeOnCall: function () {
                window.onscroll = null;
            },
            isEmptySearch: function () {
                return this.filters.title === ''
                    && this.filters.authors.length === 0
                    && this.filters.subjects.length === 0
                    && this.filters.isbn === ''
                    && this.filters.year_from === ''
                    && this.filters.year_to === ''
                    && (this.filters.language === '' || this.filters.language === null);
            },
            resetFilters: function () {
                this.filters = {
                    title: '',
                    authors: [],
                    subjects: [],
                    isbn: '',
                    year_from: '',
                    year_to: '',
                    language: null,
                    offset: 0,
                    limit: 48,
                    showFree: false,
                }
            },
            handleTutorial: function () {
                // use the name to check if user is logged in or not because hasDoneTutorial is null for people that
                // already had the app installed when tutorial was launched
                if (this.authUser.hasDoneTutorial === 'true' || this.authUser.name === null) {
                    return;
                }

                if (this.tutorialStep === 1) {
                    this.tutorialPopovers.getStarted = true;
                }
                if (this.tutorialStep === 2) {
                    this.tutorialPopovers.searchForm = true;
                }
                if (this.tutorialStep === 3) {
                    this.tutorialPopovers.selectBook = true;
                }

                this.componentRefreshHack += 3; // without this the popover components don't load on page refresh
            },
            goToTutorialSearchFormStep: function () {
                this.tutorialPopovers.getStarted = false;
                this.tutorialPopovers.searchForm = true;
                this.tutorialStepAction(2);
            },
            goToTutorialSelectBookStep: function () {
                this.tutorialPopovers.searchForm = false;
                this.tutorialPopovers.selectBook = true;
                window.scrollTo({
                    top: this.$refs.searchResults.$el.getBoundingClientRect().top - 250,
                    behavior: "smooth"
                });
                this.tutorialStepAction(3);
            },
            ...mapActions({
                tutorialStepAction: 'Setting/tutorialStepAction',
            }),
        },
        watch: {},
        metaInfo: function () {
            return {
                title: this.$t('menu.freeBooks'),
                meta: [
                    {property: 'og:title', content: this.$t('menu.freeBooks')},
                    {property: 'og:url', content: window.location.origin + window.location.pathname},
                ],
            };
        },
    };
</script>

<style lang="scss">
    @import "../../node_modules/vue-multiselect/dist/vue-multiselect.min.css";

    .w-48 {
        width: 48% !important;
    }

    .multiselect{
        .multiselect__tags {
            width: 100%;
            height: 40px;
            font-size: 13px;
            border-radius: 5px;
            border: none;

            .multiselect__input, .multiselect__placeholder {
                border: none;
                padding-left: 8px;
                padding-top: 2px;
            }

            .multiselect__placeholder {
                padding-left: 8px;
            }
        }

        .multiselect__content-wrapper {
            border: none;

            .multiselect__option--highlight {
                &::after {
                    content: none;
                }
            }
        }
    }

    [mode="dark"] {
        .multiselect{
            .multiselect__tags {
                background: var(--iq-dark-card);
                color: #757575;

                .multiselect__input, .multiselect__placeholder {
                    background: var(--iq-dark-card);
                    color: #757575;
                }
            }

            .multiselect__content-wrapper {
                background: var(--iq-dark-card);

                .multiselect__option, .multiselect__option--selected {
                    background: var(--iq-dark-card);
                    color: #757575;
                }

                .multiselect__option--highlight {
                    background: #0a2027;
                    color: #757575;
                }
            }

            .multiselect__spinner {
                background: var(--iq-dark-card);
            }
        }
    }

    [mode="light"] {
        input.multiselect__input, span.multiselect__placeholder, select.search-input {
            background: var(--iq-white) !important;
            color: var(--iq-dark) !important;
        }

        .multiselect {
            box-shadow: 0 4px 20px 0 rgb(44 101 144 / 10%);
        }
    }
</style>
