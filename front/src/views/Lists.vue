<template>
    <b-container fluid>
        <b-row>
            <b-col lg="12" class="mb-5">
                <router-link to="/add-book-list" class="btn btn-primary text-white">{{ $t('lists.createList') }}</router-link>
            </b-col>
            <b-col lg="12">
                <iq-card className=" iq-card-block iq-card-stretch iq-card-height"
                         headerClass=" align-items-center position-relative">
                    <template v-slot:headerTitle>
                        <h4 class="card-title mb-0">{{ $t('lists.liked') }}</h4>
                    </template>
                    <template v-slot:headerAction>
                        <router-link to="/book-lists/likes" class="btn btn-primary text-white">{{ $t('lists.seeAll') }}</router-link>
                    </template>
                    <template v-slot:body>
                        <ul class="list-inline p-0 m-0">
                            <li v-for="(book, indexBook) in likes" :key="indexBook" class="checkout-product">
                                <b-row class="align-items-center">
                                    <b-col sm="2">
                                         <span class="checkout-product-img">
                                             <router-link :to="{name: 'book', params: {id: book.uuid}}">
                                                 <BookCover :cover="book.cover"></BookCover>
                                             </router-link>
                                         </span>
                                    </b-col>
                                    <b-col sm="10">
                                        <div class="checkout-product-details">
                                            <router-link :to="{name: 'book', params: {id: book.uuid}}">
                                                <h5>{{ book.title }}</h5>
                                            </router-link>
                                        </div>
                                    </b-col>
                                </b-row>
                            </li>
                        </ul>
                    </template>
                </iq-card>
            </b-col>
            <b-col v-for="(list, index) in lists" :key="index" lg="12">
                <iq-card className=" iq-card-block iq-card-stretch iq-card-height" headerClass=" align-items-center position-relative">
                    <template v-slot:headerTitle>
                        <h4 class="card-title mb-0">{{ list.name }}</h4>
                    </template>
                    <template v-slot:headerAction>
                        <router-link :to="`/book-lists/${list.id}`" class="btn btn-primary text-white">{{ $t('lists.seeAll') }}</router-link>
                    </template>
                    <template v-slot:body>
                        <ul class="list-inline p-0 m-0">
                            <li v-for="(book, indexBook) in list.books" :key="indexBook" class="checkout-product">
                                <b-row class="align-items-center">
                                    <b-col sm="2">
                                         <span class="checkout-product-img">
                                             <router-link :to="{name: 'book', params: {id: book.uuid}}">
                                                 <BookCover :cover="book.cover"></BookCover>
                                             </router-link>
                                         </span>
                                    </b-col>
                                    <b-col sm="10">
                                        <div class="checkout-product-details">
                                            <router-link :to="{name: 'book', params: {id: book.uuid}}">
                                                <h5>{{ book.title }}</h5>
                                            </router-link>
                                        </div>
                                    </b-col>
                                </b-row>
                            </li>
                        </ul>
                    </template>
                </iq-card>
            </b-col>
            <b-col v-for="(readStatusBooks, index) in readStatuses" v-if="readStatusBooks.length > 0" :key="index" lg="12">
                <iq-card className=" iq-card-block iq-card-stretch iq-card-height" headerClass=" align-items-center position-relative">
                    <template v-slot:headerTitle>
                        <h4 class="card-title mb-0">{{ $t('bookStatuses.' + index) }}</h4>
                    </template>
                    <template v-slot:headerAction>
                        <router-link :to="`/book-lists/statuses?type=${index}`" class="btn btn-primary text-white">{{ $t('lists.seeAll') }}</router-link>
                    </template>
                    <template v-slot:body>
                        <ul class="list-inline p-0 m-0">
                            <li v-for="(book, indexBook) in readStatusBooks" :key="indexBook" class="checkout-product">
                                <b-row class="align-items-center">
                                    <b-col sm="2">
                                         <span class="checkout-product-img">
                                             <router-link :to="{name: 'book', params: {id: book.uuid}}">
                                                 <BookCover :cover="book.cover"></BookCover>
                                             </router-link>
                                         </span>
                                    </b-col>
                                    <b-col sm="10">
                                        <div class="checkout-product-details">
                                            <router-link :to="{name: 'book', params: {id: book.uuid}}">
                                                <h5>{{ book.title }}</h5>
                                            </router-link>
                                        </div>
                                    </b-col>
                                </b-row>
                            </li>
                        </ul>
                    </template>
                </iq-card>
            </b-col>
            <b-col lg="12">
                <iq-card className=" iq-card-block iq-card-stretch iq-card-height"
                         headerClass=" align-items-center position-relative">
                    <template v-slot:headerTitle>
                            <h4 class="card-title mb-0">{{ $t('lists.authorsFollowed') }}</h4>
                    </template>
                    <template v-slot:headerAction></template>
                    <template v-slot:body>
                        <ul class="list-inline p-0 m-0">
                            <li v-for="(author, indexAuthor) in authors" :key="indexAuthor" class="checkout-product">
                                <b-row class="align-items-center">
                                    <b-col sm="2">
                                         <span class="checkout-product-img">
                                             <router-link :to="`/author/${author.id}`">
                                                 <ProfilePicture :url="author.photo" className="rounded-circle img-fluid fa-4x" />
                                             </router-link>
                                         </span>
                                    </b-col>
                                    <b-col sm="10">
                                        <div class="checkout-product-details">
                                            <router-link :to="`/author/${author.id}`">
                                                <h5>{{ author.name }}</h5>
                                            </router-link>
                                        </div>
                                    </b-col>
                                </b-row>
                            </li>
                        </ul>
                    </template>
                </iq-card>
            </b-col>
        </b-row>
    </b-container>
</template>

<script>
    import {core} from '../config/pluginInit';
    import {mapGetters} from 'vuex';
    import HttpClient from '../services/HttpClient';
    import BookCover from '../components/BookCover';
    import ProfilePicture from '../components/ProfilePicture';

    export default {
        components: {
            BookCover,
            ProfilePicture,
        },
        mounted: function() {
            core.index();
            this.getLists();
            this.getLikes();
            this.getFollowedAuthors();
            this.getReadStatuses();
        },
        data: function() {
            return {
                lists: [],
                likes: [],
                authors: [],
                readStatuses: [],
            };
        },
        computed: {
            ...mapGetters({
                lang: 'Setting/langState',
            }),
        },
        methods: {
            getLists: async function () {
                let response = await HttpClient.get('/book-lists');
                if (response.status === 200) {
                    this.lists = response.data;
                }
            },
            getLikes: async function () {
                let response = await HttpClient.get('/likes?full=0');
                if (response.status === 200) {
                    this.likes = response.data;
                }
            },
            getFollowedAuthors: <AUTHORS>
                let response = await HttpClient.get('/authors/followed');
                if (response.status === 200) {
                    this.authors = response.data;
                }
            },
            getReadStatuses: async function () {
                let response = await HttpClient.get('/book-read-statuses?full=0');
                if (response.status === 200) {
                    this.readStatuses = response.data;
                }
            },
        },
        watch: {},
        metaInfo: function () {
            return {
                title: this.$t('menu.bookLists'),
                meta: [
                    {property: 'og:title', content: this.$t('menu.bookLists')},
                    {property: 'og:url', content: window.location.origin + window.location.pathname},
                ],
            };
        },
    };
</script>

<style lang="scss">
    .checkout-product-img .w-cover {
        min-height: auto;
        max-height: 105px;
    }
</style>
