<template>
    <b-container fluid>
        <b-row>
            <b-col lg="12">
                <iq-card className=" iq-card-block iq-card-stretch iq-card-height" headerClass=" align-items-center position-relative">
                    <template v-slot:headerTitle>
                        <h4 class="card-title mb-0">{{ $t('achievements.achievements') }}</h4>
                    </template>
                    <template v-slot:headerAction></template>
                    <template v-slot:body>
                        <ul class="list-inline p-0 m-0">
                            <li v-for="(achievements, index) in achievements" :key="index" class="checkout-product">
                                <b-row class="align-items-center">
                                    <b-col sm="2">
                                         <span class="checkout-product-img">
                                             <ProfilePicture url="" className="rounded-circle img-fluid fa-4x" />
                                         </span>
                                    </b-col>
                                    <b-col sm="10">
                                        <div class="checkout-product-details">
                                            <h5>{{ achievements.name }}</h5>
                                        </div>
                                    </b-col>
                                </b-row>
                            </li>
                        </ul>
                    </template>
                </iq-card>
            </b-col>
        </b-row>
    </b-container>
</template>

<script>
    import {core} from '../config/pluginInit';
    import HttpClient from '../services/HttpClient';
    import BookCover from '../components/BookCover';
    import ProfilePicture from '../components/ProfilePicture';

    export default {
        components: {
            BookCover,
            ProfilePicture,
        },
        mounted: function() {
            core.index();
            this.getAchievements();
        },
        data: function() {
            return {
                achievements: [],
            };
        },
        computed: {},
        methods: {
            getAchievements: async function () {
                let response = await HttpClient.get('/achievements');
                if (response.status === 200) {
                    this.achievements = response.data;
                }
            },
        },
        watch: {},
        metaInfo: function () {
            return {
                title: this.$t('menu.Achievements'),
                meta: [
                    {property: 'og:title', content: this.$t('menu.Achievements')},
                    {property: 'og:url', content: window.location.origin + window.location.pathname},
                ],
            };
        },
    };
</script>

<style lang="scss">

</style>
