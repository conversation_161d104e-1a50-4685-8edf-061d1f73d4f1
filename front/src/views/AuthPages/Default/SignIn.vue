<template>
    <div>
        <h3 class="mb-0 text-center text-white">{{ $t('auth.login') }}</h3>
        <ValidationObserver ref="formValidator" v-slot="{ handleSubmit }">
            <form class="form-text" novalidate @submit.prevent="handleSubmit(onSubmit)">
                <ValidationProvider vid="email" name="email" rules="required|email" v-slot="{ errors }">
                    <div class="form-group">
                        <label for="emailInput">{{ $t('fields.email') }}</label>
                        <input type="email" :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"
                            id="emailInput" aria-describedby="emailHelp" v-model="user.email" required>
                        <div class="invalid-feedback">
                            <span>{{errors[0]}}</span>
                        </div>
                    </div>
                </ValidationProvider>
                <ValidationProvider vid="password" name="password" rules="required" v-slot="{ errors }">
                    <div class="form-group">
                        <label for="passwordInput">{{ $t('fields.password') }}</label>
                        <router-link to="/password-reset" class="float-right text-dark">
                            {{ $t('auth.forgotPassword') }}
                        </router-link>
                        <input type="password" :class="'form-control mb-0' +(errors.length > 0 ? ' is-invalid' : '')"
                            id="passwordInput" v-model="user.password" required>
                        <div class="invalid-feedback">
                            <span>{{errors[0]}}</span>
                        </div>
                    </div>
                </ValidationProvider>
                <div class="sign-info text-center">
                    <button type="submit" class="btn btn-primary d-block w-100 mb-2">{{ $t('auth.login') }}</button>
                    <span>- or -</span>
                    <SocialLogin/>
                    <span class="text-dark dark-color d-inline-block line-height-2 mt-3">{{ $t('auth.noAccount') }}
                    <router-link to="/register" class="text-white">{{ $t('auth.signup') }}</router-link>
                </span>
                </div>
            </form>
        </ValidationObserver>
    </div>
</template>

<script>
    import Auth from '../../../services/Auth';
    import SocialLogin from './SocialLogin'

    export default {
        components: {
            SocialLogin,
        },
        mounted: function() {},
        data: function() {
            return {
                user: {
                    email: '',
                    password: '',
                },
            };
        },
        methods: {
            onSubmit: async function () {
                let response = await Auth.login(this.user.email, this.user.password);

                if (response.status === 200) {
                    await this.$router.push({name: 'homepage'});
                }

                if (response.status === 422) {
                    this.$refs.formValidator.setErrors(response.data.errors);
                }
            },
        },
        metaInfo: function () {
            return {
                title: this.$t('auth.login'),
                meta: [
                    {property: 'og:title', content: this.$t('auth.login')},
                    {property: 'og:url', content: window.location.origin + window.location.pathname},
                ],
            };
        },
    };
</script>

<style lang="scss">

</style>
