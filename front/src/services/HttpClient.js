import axios from 'axios';
import settings from '../config/settings';

let axiosClient = axios.create({
    baseURL: settings.webBaseURL,
    headers: {},
});
let pendingCalls = 0;

axiosClient.interceptors.request.use(function (config) {
    if (config.isExternal === true) {
        return config;
    }

    pendingCalls++;
    window.vm.$Progress.start();

    let token = localStorage.getItem('token')
    if (token !== null && config.headers.Authorization === undefined) {
        config.headers.Authorization = `Bearer ${token}`;
    }

    let locale = localStorage.getItem('user.language')
    if (locale !== null) {
        config.headers['X-locale'] = locale;
    }

    return config;
});

axiosClient.interceptors.response.use(response => {
    if (response.config.isExternal === true) {
        return response;
    }

    pendingCalls--;
    if (pendingCalls === 0) {
        window.vm.$Progress.finish();
    }

    return response;
}, error => {
    pendingCalls--;
    if (pendingCalls === 0) {
        window.vm.$Progress.finish();
    }

    throw error;
});

// noinspection JSCheckFunctionSignatures
export default {
    get: async function (url, data, config) {
        if (typeof data === 'object' && data !== null) {
            let glue = url.includes('?') ? '&' : '?';
            let queryParams = new URLSearchParams(data);
            url += glue + queryParams.toString();
        }

        try {
            return await axiosClient.get(url, config);
        } catch (error) {
            return error.response;
        }
    },
    post: async function (url, data, config) {
        try {
            return await axiosClient.post(url, data, config);
        } catch (error) {
            return error.response;
        }
    },
    put: async function (url, data, config) {
        try {
            return await axiosClient.put(url, data, config);
        } catch (error) {
            return error.response;
        }
    },
    delete: async function (url, config) {
        try {
            return await axiosClient.delete(url, config);
        } catch (error) {
            return error.response;
        }
    },
};
