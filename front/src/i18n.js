import Vue from 'vue';
import VueI18n from 'vue-i18n';
import { configure } from 'vee-validate';
import enValidationMessages from 'vee-validate/dist/locale/en';
import roValidationMessages from 'vee-validate/dist/locale/ro';

Vue.use(VueI18n);

function loadLocaleMessages()
{
    const locales = require.context('./locales', true, /[A-Za-z0-9-_,\s]+\.json$/i);
    const messages = {};
    locales.keys().forEach(key => {
        const matched = key.match(/([A-Za-z0-9-_]+)\./i);
        if (matched && matched.length > 1) {
            const locale = matched[1];
            messages[locale] = locales(key);
        }
    });

    messages.en.validations = enValidationMessages;
    messages.ro.validations = roValidationMessages;

    return messages;
}

let i18n = new VueI18n({
    locale: localStorage.getItem('user.language') || process.env.VUE_APP_DEFAULT_LANGUAGE,
    fallbackLocale: process.env.VUE_APP_DEFAULT_LANGUAGE,
    messages: loadLocaleMessages(),
});

configure({
    defaultMessage: (field, values) => {
        values._field_ = i18n.t(`fields.${field}`);

        return i18n.t(`validations.messages.${values._rule_}`, values);
    }
});

export default i18n;
