<!DOCTYPE html>
<html lang="en" dir="ltr" mode="dark">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
        <link rel="manifest" href="/site.webmanifest">
        <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5">
        <meta name="msapplication-TileColor" content="#2b5797">
        <meta name="theme-color" content="#ffffff">
        <meta name="description" content="Find and read over 500.000 books or listen to over 15.000 audiobooks, all for free. You can also like, dislike, share and organize books (free or otherwise) into lists. You will get custom recommendations based on your likes and favorite authors.">
        <title>Liberom</title>
        <script type="application/ld+json">
            {
                "@context": "https://schema.org",
                "@type": "Organization",
                "url": "https://liberom.com",
                "logo": "https://liberom.com/img/logo.png"
            }
        </script>
        <% if (VUE_APP_GOOGLE_ADSENSE) { %>
            <script async
                src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=<%= VUE_APP_GOOGLE_ADSENSE %>"
                crossorigin="anonymous"
            ></script>
        <% } %>
        <% if (VUE_APP_GOOGLE_TAG_MANAGER) { %>
            <script async src="https://www.googletagmanager.com/gtag/js?id=<%= VUE_APP_GOOGLE_TAG_MANAGER %>"></script>
            <script>
                window.dataLayer = window.dataLayer || [];
                function gtag() {
                    dataLayer.push(arguments);
                }
                gtag('js', new Date());
                gtag('config', '<%= VUE_APP_GOOGLE_TAG_MANAGER %>');
            </script>
        <% } %>
    </head>
    <body class="">
        <div id="app"></div>
    </body>
</html>
