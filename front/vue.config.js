let webpack = require('webpack');
let plugins = process.env.NODE_ENV === 'production' ? [] : [
    new webpack.optimize.LimitChunkCountPlugin({
        maxChunks: 1
    })
];

module.exports = {
    publicPath: '/',
    configureWebpack: {
        plugins: plugins,
        resolve: {
            alias: {
                'vue$': 'vue/dist/vue.common.js',
                'jquery': 'jquery/src/jquery.js',
            },
        },
    },
    productionSourceMap: false,
    pluginOptions: {
        i18n: {
            locale: 'en',
            fallbackLocale: 'en',
            localeDir: 'locales',
            enableInSFC: false,
        },
    },
    chainWebpack: config => {
        // should probably also use url-loader which is a npm package since I don't see file-loader installed
        config.module.rule('pdf')
            .test(/\.pdf$/)
            .use('file-loader').loader('file-loader');
        config.module.rule('svg')
            .test(/\.svg$/)
            .use('url-loader').loader('url-loader');
        config.plugins.delete('pwa');
    },
    devServer: {
        client: { // domain needs to be added in hosts file as 127.0.0.1 ip
            webSocketURL: `auto://${process.env.VUE_APP_DOMAIN}:443/ws`
        },
        port: 443,
        server: 'https',
        allowedHosts: 'all',
    },
};
