{"exclude": ["bootstrap", "config", "database", "public", "resources", "routes", "storage", "tests", "vendor"], "notPath": ["Http/Controllers/Controller.php"], "preset": "psr12", "rules": {"align_multiline_comment": {"comment_type": "phpdocs_like"}, "array_indentation": true, "array_push": true, "array_syntax": true, "assign_null_coalescing_to_coalesce_equal": true, "backtick_to_shell_exec": true, "binary_operator_spaces": true, "blank_line_after_opening_tag": false, "blank_line_before_statement": {"statements": ["return", "throw", "try"]}, "cast_spaces": {"space": "none"}, "class_attributes_separation": {"elements": {"case": "none", "const": "none", "method": "one", "property": "none", "trait_import": "none"}}, "class_definition": {"single_item_single_line": true, "single_line": true}, "class_reference_name_casing": true, "clean_namespace": true, "combine_nested_dirname": true, "concat_space": {"spacing": "one"}, "date_time_create_from_format_call": true, "declare_parentheses": true, "declare_strict_types": true, "dir_constant": true, "empty_loop_body": {"style": "semicolon"}, "empty_loop_condition": true, "ereg_to_preg": true, "error_suppression": {"mute_deprecation_error": false, "noise_remaining_usages": true}, "escape_implicit_backslashes": true, "explicit_indirect_variable": true, "explicit_string_variable": true, "final_class": true, "fully_qualified_strict_types": true, "function_to_constant": true, "function_typehint_space": true, "general_phpdoc_annotation_remove": {"annotations": ["method", "property-read"]}, "get_class_to_class_keyword": true, "global_namespace_import": {"import_constants": true, "import_functions": true}, "implode_call": true, "include": true, "integer_literal_case": true, "is_null": true, "lambda_not_used_import": true, "list_syntax": true, "logical_operators": true, "magic_constant_casing": true, "magic_method_casing": true, "mb_str_functions": true, "method_chaining_indentation": true, "modernize_strpos": true, "modernize_types_casting": true, "multiline_whitespace_before_semicolons": true, "native_function_casing": true, "native_function_type_declaration_casing": true, "no_alias_functions": true, "no_alternative_syntax": true, "no_binary_string": true, "no_blank_lines_after_phpdoc": true, "no_empty_comment": true, "no_empty_phpdoc": true, "no_empty_statement": true, "no_extra_blank_lines": {"tokens": ["attribute", "break", "case", "continue", "curly_brace_block", "default", "extra", "parenthesis_brace_block", "return", "square_brace_block", "switch", "throw", "use", "use_trait"]}, "no_homoglyph_names": true, "no_leading_namespace_whitespace": true, "no_multiline_whitespace_around_double_arrow": true, "no_null_property_initialization": true, "no_php4_constructor": true, "no_short_bool_cast": true, "no_singleline_whitespace_before_semicolons": true, "no_spaces_around_offset": true, "no_superfluous_elseif": true, "no_superfluous_phpdoc_tags": true, "no_trailing_comma_in_singleline": true, "no_unneeded_control_parentheses": true, "no_unneeded_curly_braces": true, "no_unneeded_final_method": true, "no_unneeded_import_alias": true, "no_unset_cast": true, "no_unused_imports": true, "no_useless_concat_operator": true, "no_useless_else": true, "no_useless_nullsafe_operator": true, "no_useless_return": true, "no_useless_sprintf": true, "no_whitespace_before_comma_in_array": true, "non_printable_character": true, "normalize_index_brace": true, "object_operator_without_whitespace": true, "octal_notation": true, "operator_linebreak": true, "php_unit_construct": true, "php_unit_dedicate_assert": true, "php_unit_dedicate_assert_internal_type": true, "php_unit_expectation": true, "php_unit_fqcn_annotation": true, "php_unit_method_casing": true, "php_unit_mock": true, "php_unit_mock_short_will_return": true, "php_unit_namespaced": true, "php_unit_no_expectation_annotation": true, "php_unit_set_up_tear_down_visibility": true, "phpdoc_single_line_var_spacing": true, "pow_to_exponentiation": true, "protected_to_private": true, "random_api_migration": true, "semicolon_after_instruction": true, "set_type_to_cast": true, "simple_to_complex_string_variable": true, "simplified_if_return": true, "single_line_comment_spacing": true, "single_quote": true, "space_after_semicolon": true, "standardize_increment": true, "standardize_not_equals": true, "string_length_to_empty": true, "switch_continue_to_break": true, "ternary_to_elvis_operator": true, "ternary_to_null_coalescing": true, "trailing_comma_in_multiline": true, "trim_array_spaces": true, "types_spaces": true, "unary_operator_spaces": true, "use_arrow_functions": true, "void_return": true, "whitespace_after_comma_in_array": true, "yoda_style": {"equal": false, "identical": false}}}