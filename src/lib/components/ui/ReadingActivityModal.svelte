<script lang="ts">
    import {createEventDispatcher} from 'svelte';
    import {CalendarDate, type DateValue, getLocalTimeZone, today} from '@internationalized/date';
    import {onMount} from 'svelte';
    import LanguagesApiClient from '$lib/api/LanguagesApiClient';
    import type {CreateReadingActivityData, UpdateReadingActivityData} from '$lib/api/ReadingActivitiesApiClient';
    import ReadingActivitiesApiClient from '$lib/api/ReadingActivitiesApiClient';
    import SpinnerSvg from '$lib/components/svg/SpinnerSvg.svelte';
    import Button from '$lib/components/ui/Button.svelte';
    import DatePicker from '$lib/components/ui/DatePicker.svelte';
    import Modal from '$lib/components/ui/Modal.svelte';
    import NumberInput from '$lib/components/ui/NumberInput.svelte';
    import Select from '$lib/components/ui/Select.svelte';
    import type Book from '$lib/domain/Book';
    import type Language from '$lib/domain/Language';
    import type ReadingActivity from '$lib/domain/ReadingActivity';
    import {t} from '$lib/localization/Localization';
    import ToastNotificationsStore from '$lib/stores/ToastNotificationsStore';

    export let book: Book;
    export let activity: ReadingActivity | null = null;
    export let open = false;

    const dispatch = createEventDispatcher<{
        activitySaved: void;
        modalClosed: void;
    }>();

    let isEditing = false;
    let selectedLanguage: string = '';
    let startDate: DateValue | undefined = today(getLocalTimeZone());
    let endDate: DateValue | undefined;
    let pagesRead = '';
    let totalPages = '';
    let validationErrors: Record<string, string> = {};
    let languageOptions: string[] = [];
    let isLoading = false;

    onMount(() => {
        loadLanguages();
        setupLanguageOptions();
    });

    // Watch for changes to activity prop to set up editing mode
    $: if (activity && open) {
        setupEditMode();
    } else if (open && !activity) {
        setupAddMode();
    }

    async function loadLanguages() {
        const languagesApiClient = new LanguagesApiClient();
        const response = await languagesApiClient.index();

        if (!response.ok) {
            ToastNotificationsStore.push({
                text: $t('notifications.genericError'),
                type: 'warning'
            });
            return;
        }

        languageOptions = response.data.map((language: Language) => language.text);
    }

    function setupLanguageOptions() {
        if (book.languages.length > 0) {
            selectedLanguage = book.languages[0].text;
        }

        if (book.numberOfPages) {
            totalPages = book.numberOfPages.toString();
        }
    }

    function setupEditMode() {
        if (!activity) return;

        isEditing = true;
        populateFormFromActivity(activity);
    }

    function setupAddMode() {
        isEditing = false;
        resetForm();
    }

    function populateFormFromActivity(activity: ReadingActivity) {
        selectedLanguage = activity.language?.text || '';

        if (activity.startDate) {
            const [year, month, day] = activity.startDate.split('-').map(Number);
            startDate = new CalendarDate(year, month, day);
        }

        if (activity.endDate) {
            const [year, month, day] = activity.endDate.split('-').map(Number);
            endDate = new CalendarDate(year, month, day);
        }

        pagesRead = activity.pagesRead?.toString() || '';
        totalPages = activity.totalPages?.toString() || '';
    }

    function resetForm() {
        selectedLanguage = book.languages.length > 0 ? book.languages[0].text : '';

        startDate = today(getLocalTimeZone());
        endDate = undefined;
        pagesRead = '';
        totalPages = book.numberOfPages?.toString() || '';
        validationErrors = {};
    }

    function validateForm(): boolean {
        validationErrors = {};

        if (startDate && endDate && startDate.compare(endDate) > 0) {
            validationErrors.endDate = $t('readingActivity.validationEndDateAfterStart');
        }

        if (pagesRead && totalPages && parseInt(pagesRead) > parseInt(totalPages)) {
            validationErrors.pagesRead = $t('readingActivity.validationPagesReadExceedsTotal');
        }

        return Object.keys(validationErrors).length === 0;
    }

    async function saveActivity() {
        if (!validateForm()) {
            return;
        }

        isLoading = true;
        const apiClient = new ReadingActivitiesApiClient();

        // Find the language code from the selected language text
        const selectedLang = languageOptions.length > 0 ?
            book.languages.find(lang => lang.text === selectedLanguage) : null;

        const activityData: CreateReadingActivityData | UpdateReadingActivityData = {
            languageCode: selectedLang?.code || undefined,
            startDate: startDate ? `${startDate.year}-${startDate.month.toString().padStart(2, '0')}-${startDate.day.toString().padStart(2, '0')}` : undefined,
            endDate: endDate ? `${endDate.year}-${endDate.month.toString().padStart(2, '0')}-${endDate.day.toString().padStart(2, '0')}` : undefined,
            pagesRead: pagesRead ? parseInt(pagesRead) : undefined,
            totalPages: totalPages ? parseInt(totalPages) : undefined,
        };

        let response;
        if (isEditing && activity) {
            response = await apiClient.update(activity.id, activityData);
        } else {
            response = await apiClient.store({
                bookId: book.uuid,
                ...activityData
            });
        }

        isLoading = false;

        if (!response.ok) {
            ToastNotificationsStore.push({
                text: $t('notifications.genericError'),
                type: 'warning'
            });
            return;
        }

        ToastNotificationsStore.push({
            text: $t('readingActivity.activitySaved'),
            type: 'success'
        });

        // Close modal and dispatch event
        open = false;
        resetForm();
        dispatch('activitySaved');
    }

    function closeModal() {
        open = false;
        resetForm();
        dispatch('modalClosed');
    }
</script>

<Modal bind:open>
    <div class="mb-4 flex justify-between rounded-t sm:mb-5">
        <div class="text-lg font-semibold text-gray-900 dark:text-white">
            {isEditing ? $t('readingActivity.editActivity') : $t('readingActivity.addActivity')}
        </div>
    </div>
    <form class="space-y-4" on:submit|preventDefault={saveActivity}>
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {$t('readingActivity.startDate')}
                </label>
                <div class="w-full">
                    <DatePicker bind:value={startDate}/>
                </div>
                {#if validationErrors.startDate}
                    <p class="text-red-600 text-sm mt-1">{validationErrors.startDate}</p>
                {/if}
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {$t('readingActivity.endDate')}
                </label>
                <div class="w-full">
                    <DatePicker bind:value={endDate}/>
                </div>
                {#if validationErrors.endDate}
                    <p class="text-red-600 text-sm mt-1">{validationErrors.endDate}</p>
                {/if}
            </div>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {$t('readingActivity.language')}
            </label>
            <div>
                <Select title={$t('readingActivity.language')} bind:value={selectedLanguage} options={languageOptions} />
            </div>
        </div>
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {$t('readingActivity.pagesRead')}
                </label>
                <NumberInput bind:value={pagesRead} />
                {#if validationErrors.pagesRead}
                    <p class="text-red-600 text-sm mt-1">{validationErrors.pagesRead}</p>
                {/if}
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {$t('readingActivity.totalPages')}
                </label>
                <NumberInput bind:value={totalPages} />
                {#if validationErrors.totalPages}
                    <p class="text-red-600 text-sm mt-1">{validationErrors.totalPages}</p>
                {/if}
            </div>
        </div>
        <div class="flex justify-end space-x-2 pt-4">
            <Button
                title="Cancel"
                callback={closeModal}
                primary={false}
            />
            <button
                type="submit"
                class="inline-flex items-center rounded-lg bg-primary-700 px-4 py-2 text-center font-medium text-sm text-white hover:bg-primary-800 focus:outline-none dark:bg-primary-600 dark:hover:bg-primary-700 disabled:opacity-50"
                disabled={isLoading}
            >
                {#if isLoading}
                    <SpinnerSvg svgClass="w-4 h-4 mr-2" />
                {/if}
                {$t('readingActivity.save')}
            </button>
        </div>
    </form>
</Modal>
