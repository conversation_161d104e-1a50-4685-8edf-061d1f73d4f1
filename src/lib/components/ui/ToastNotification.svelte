<script lang="ts">
    import {onDestroy} from 'svelte';
    import {linear} from 'svelte/easing';
    import {type Tweened, tweened} from 'svelte/motion';
    import CheckSvg from '$lib/components/svg/CheckSvg.svelte';
    import ExclamationMarkSvg from '$lib/components/svg/ExclamationMarkSvg.svelte';
    import XSvg from '$lib/components/svg/XSvg.svelte';
    import type {toastNotificationTypeType} from '$lib/stores/ToastNotificationsStore';

    export let text: string;
    export let onCloseCallback: (() => void) | undefined = undefined;
    export let type: toastNotificationTypeType = 'warning';

    const notificationTTLInMs = 5000;
    const progressBartStartPosition = 1;
    const progressBartEndPosition = 0;
    const iconBackgroundColor = type === 'warning' ? 'bg-orange-700' : 'bg-green-700';

    let progress: Tweened<number>;
    let timeoutId: number;
    let currentProgressValue: number = progressBartStartPosition;

    function addNotificationExpiration() {
        progress = tweened(currentProgressValue, {
            duration: notificationTTLInMs * currentProgressValue,
            easing: linear,
        });
        progress.set(progressBartEndPosition);

        timeoutId = setTimeout(() => {
            if (onCloseCallback !== undefined) {
                onCloseCallback();
            }
        }, notificationTTLInMs * currentProgressValue);
    }

    function cancelNotificationExpiration() {
        currentProgressValue = $progress;
        progress.set(currentProgressValue);
        clearTimeout(timeoutId);
    }

    addNotificationExpiration();
    onDestroy(() => {
        cancelNotificationExpiration();
    });
</script>

<div
    class="relative flex w-full items-center rounded-lg bg-white p-4 shadow dark:bg-gray-800"
    role="alert"
    on:mouseenter={cancelNotificationExpiration}
    on:mouseleave={addNotificationExpiration}
>
    <progress class="absolute left-0 top-0 h-1 w-full rounded" value={$progress} />
    <div class={`inline-flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-lg ${iconBackgroundColor}`}>
        {#if type === 'warning'}
            <ExclamationMarkSvg svgClass="h-5 w-5 text-white" />
        {:else}
            <CheckSvg svgClass="h-5 w-5 text-white" />
        {/if}
        <span class="sr-only">Icon</span>
    </div>
    <div class="ms-3 text-sm font-normal">{text}</div>
    {#if onCloseCallback}
        <button
            type="button"
            class="-mx-1.5 -my-1.5 ms-auto inline-flex h-8 w-8 items-center justify-center rounded-lg bg-white p-1.5 text-gray-400 hover:bg-gray-100 hover:text-gray-900 focus:ring-2 focus:ring-gray-300 dark:bg-gray-800 dark:text-gray-500 dark:hover:bg-gray-700 dark:hover:text-white"
            aria-label="Close"
            on:click={onCloseCallback}
        >
            <span class="sr-only">Close</span>
            <XSvg />
        </button>
    {/if}
</div>
