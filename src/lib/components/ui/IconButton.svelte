<script lang="ts">
    import type {ComponentType} from 'svelte';

    export let icon: ComponentType;
    export let title: string | undefined = undefined;
    export let href: URL | undefined = undefined;
    export let callback: (() => void) | undefined = undefined;
    export let bold: boolean = false;
    export let backgroundColor: string = 'blue-700';
    export let backgroundColorDark: string = 'blue-600';
    export let backgroundColorHover: string = 'blue-800';
    export let backgroundColorHoverDark: string = 'blue-700';
</script>

{#if href}
    <a href={href} class="inline-block align-middle">
        <span
            class={`'mr-1 inline-flex items-center rounded-xl bg-${backgroundColor} p-1.5 text-center align-middle text-sm font-medium text-white hover:bg-${backgroundColorHover} focus:outline-none focus:ring-4 focus:ring-blue-300 dark:bg-${backgroundColorDark} dark:hover:bg-${backgroundColorHoverDark} dark:focus:ring-blue-800'`}
        >
            <svelte:component this={icon} svgClass="w-5 h-5" />
        </span>
        {#if title}
            <span class="align-middle" class:font-semibold={bold}>{title}</span>
        {/if}
    </a>
{:else}
    <button type={callback === undefined ? 'submit' : 'button'} on:click={callback}>
        <span
            class={`'mr-1 inline-flex items-center rounded-xl bg-${backgroundColor} p-1.5 text-center align-middle text-sm font-medium text-white hover:bg-${backgroundColorHover} focus:outline-none focus:ring-4 focus:ring-blue-300 dark:bg-${backgroundColorDark} dark:hover:bg-${backgroundColorHoverDark} dark:focus:ring-blue-800'`}
        >
            <svelte:component this={icon} svgClass="w-5 h-5" />
        </span>
        {#if title}
            <span class="align-middle" class:font-semibold={bold}>{title}</span>
        {/if}
    </button>
{/if}
