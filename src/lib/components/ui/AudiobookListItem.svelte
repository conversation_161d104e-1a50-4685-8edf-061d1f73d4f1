<script lang="ts">
    import CalendarSvg from '$lib/components/svg/CalendarSvg.svelte';
    import GiftSvg from '$lib/components/svg/GiftSvg.svelte';
    import LanguageSvg from '$lib/components/svg/LanguageSvg.svelte';
    import VolumeSvg from '$lib/components/svg/VolumeSvg.svelte';
    import type Audiobook from '$lib/domain/Audiobook';
    import {t} from '$lib/localization/Localization';
    import AppRoutes from '$routes/AppRoutes';

    export let audiobook: Audiobook;
</script>

<a href={AppRoutes.audiobook(audiobook.uuid)} class="flex flex-col">
    <div
        class="rounded-lg border border-gray-200 bg-gray-50 p-4 shadow hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:hover:bg-gray-600"
    >
        <div class="flex-1 sm:flex">
            <div class="relative mb-3 flex h-32 w-32 items-center justify-center sm:mb-0">
                <VolumeSvg svgClass="h-24 w-24 rounded-2xl" />
            </div>
            <div class="flex-auto justify-evenly sm:ml-5">
                <div class="flex items-center justify-between sm:mt-2">
                    <div class="flex items-center">
                        <div class="flex flex-col">
                            <div
                                class="line-clamp-1 w-full flex-none text-lg font-bold leading-none text-gray-700 dark:text-white"
                            >
                                {audiobook.title}
                            </div>
                            <div class="my-1 flex-auto text-gray-500 dark:text-gray-400">
                                <span>{audiobook.creator}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex pt-2 text-sm text-gray-500 dark:text-gray-400">
                    {#if audiobook.language}
                        <div class="inline-flex flex-1 items-center">
                            <LanguageSvg svgClass="mr-2 h-5 w-5" />
                            <span>{audiobook.language.text}</span>
                        </div>
                    {/if}
                    {#if audiobook.publicationDate}
                        <div class="inline-flex flex-1 items-center">
                            <CalendarSvg svgClass="mr-2 h-5 w-5" />
                            <span>{audiobook.publicationDate}</span>
                        </div>
                    {/if}
                    <div class="inline-flex flex-1 items-center">
                        <GiftSvg svgClass="mr-2 h-5 w-5" />
                        <span>{$t('book.audiobook')}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</a>
