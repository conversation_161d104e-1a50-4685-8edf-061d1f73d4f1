<script lang="ts">
    import type {ComponentType} from 'svelte';

    export let title: string;
    export let href: URL | undefined = undefined;
    export let callback: (() => void) | undefined = undefined;
    export let primary: boolean = true;
    export let extraClass: string = '';
    export let icon: ComponentType | undefined = undefined;

    const cssClass = primary
        ? `inline-block rounded-lg bg-primary-700 px-4 py-2 text-center font-medium text-sm text-white hover:bg-primary-800 focus:outline-none dark:bg-primary-600 dark:hover:bg-primary-700 ${extraClass}`
        : `inline-block rounded-lg border border-gray-200 bg-white px-4 py-2 font-medium text-sm text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white ${extraClass}`;
</script>

{#if href}
    <a href={href} class={cssClass}>
        {#if icon}
            <svelte:component this={icon} svgClass="w-5 h-5 inline-block mr-1" />
        {/if}
        <span class="inline-block align-middle">{title}</span>
    </a>
{:else}
    <button type={callback === undefined ? 'submit' : 'button'} class={cssClass} on:click={callback}>
        {#if icon}
            <svelte:component this={icon} svgClass="w-5 h-5 inline-block mr-1" />
        {/if}
        <span class="inline-block align-middle">{title}</span>
    </button>
{/if}
