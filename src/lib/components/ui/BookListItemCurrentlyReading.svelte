<script lang="ts">
    import {createEventDispatcher} from 'svelte';
    import BookOpenSvg from '$lib/components/svg/BookOpenSvg.svelte';
    import GiftSvg from '$lib/components/svg/GiftSvg.svelte';
    import LanguageSvg from '$lib/components/svg/LanguageSvg.svelte';
    import PencilSvg from '$lib/components/svg/PencilSvg.svelte';
    import TrashSvg from '$lib/components/svg/TrashSvg.svelte';
    import type Book from '$lib/domain/Book';
    import type ReadingActivity from '$lib/domain/ReadingActivity';
    import {t} from '$lib/localization/Localization';
    import AppRoutes from '$routes/AppRoutes';

    export let book: Book;
    export let deleteCallback: ((bookIdentifier: string) => void) | undefined = undefined;

    const dispatch = createEventDispatcher<{
        editActivity: { book: Book; activity: ReadingActivity | null };
    }>();

    // Calculate progress from the most recent reading activity
    function getLatestActivity(): ReadingActivity | null {
        if (!book.readingActivity || book.readingActivity.length === 0) {
            return null;
        }

        // Sort by startDate descending to get the most recent activity
        const sortedActivities = [...book.readingActivity].sort((a, b) => {
            const dateA = a.startDate ? new Date(a.startDate).getTime() : 0;
            const dateB = b.startDate ? new Date(b.startDate).getTime() : 0;
            return dateB - dateA;
        });

        return sortedActivities[0];
    }

    function calculateProgress(): { percentage: number; text: string } {
        const latestActivity = getLatestActivity();

        if (!latestActivity || latestActivity.pagesRead === null || latestActivity.totalPages === null || latestActivity.totalPages === 0) {
            return { percentage: 0, text: '0%' };
        }

        const percentage = Math.round((latestActivity.pagesRead / latestActivity.totalPages) * 100);
        return { percentage, text: `${percentage}%` };
    }

    function handleEditClick(event: Event) {
        event.preventDefault();
        event.stopPropagation();

        const latestActivity = getLatestActivity();
        dispatch('editActivity', { book, activity: latestActivity });
    }

    $: progress = calculateProgress();
</script>

<a href={AppRoutes.book(book.uuid)} class="group block">
    <div
        class="relative flex h-full flex-col rounded-lg border border-gray-200 bg-gray-50 p-3 shadow transition-all hover:bg-gray-100 hover:shadow-md dark:border-gray-600 dark:bg-gray-700 dark:hover:bg-gray-600"
    >
        {#if deleteCallback !== undefined}
            <button
                type="button"
                class="absolute right-2 top-2 z-10 rounded-full p-1 text-gray-400 transition-all hover:bg-red-100 hover:text-red-600 dark:text-gray-500 dark:hover:bg-red-900/20 dark:hover:text-red-400"
                on:click={(event) => {
                    event.preventDefault();
                    event.stopPropagation();
                    deleteCallback(book.uuid);
                }}
            >
                <TrashSvg />
            </button>
        {/if}

        <!-- Edit Button -->
        <button
            type="button"
            class="absolute {deleteCallback !== undefined ? 'right-2 top-10' : 'right-2 top-2'} z-10 rounded-full p-1 text-gray-400 transition-all hover:bg-blue-100 hover:text-blue-600 dark:text-gray-500 dark:hover:bg-blue-900/20 dark:hover:text-blue-400"
            on:click={handleEditClick}
            title={$t('readingActivity.edit')}
        >
            <PencilSvg svgClass="h-4 w-4" />
        </button>

        <div class="mb-3 flex justify-center">
            {#if book.cover}
                <img src={book.cover} class="h-32 rounded-lg object-cover shadow-sm" alt="Book cover" />
            {:else}
                <div class="flex h-32 items-center justify-center rounded-lg bg-gray-200 shadow-sm dark:bg-gray-600">
                    <BookOpenSvg svgClass="h-8 w-8 text-gray-400 dark:text-gray-500" />
                </div>
            {/if}
        </div>
        <div class="flex flex-1 flex-col space-y-2">
            <div class="text-center">
                <div class="h-10 flex items-start justify-center">
                    <h3 class="line-clamp-2 text-sm font-semibold leading-tight text-gray-700 dark:text-white">
                        {book.title}
                    </h3>
                </div>
                <p class="mt-1 line-clamp-1 text-sm text-gray-500 dark:text-gray-400">
                    {book.authors.join(', ')}
                </p>
            </div>
            <div class="mt-auto space-y-1">
                <div class="flex items-center justify-center text-sm text-gray-500 dark:text-gray-400">
                    <LanguageSvg svgClass="mr-1 h-3 w-3" />
                    <span class="line-clamp-1">{book.languages.map((language) => language.text).join(', ')}</span>
                </div>

                <!-- Fixed height container for consistent alignment -->
                <div class="h-5 flex items-center justify-center">
                    {#if book.isFree}
                        <div class="flex items-center justify-center text-sm text-gray-500 dark:text-gray-400">
                            <GiftSvg svgClass="mr-1 h-3 w-3" />
                            <span>{$t('book.read')}</span>
                        </div>
                    {/if}
                </div>

                <!-- Progress Bar -->
                <div class="mt-2 space-y-1">
                    <div class="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400">
                        <span>{$t('readingActivity.progress')}</span>
                        <span>{progress.text}</span>
                    </div>
                    <div class="w-full bg-gray-300 rounded-full h-2 dark:bg-gray-500">
                        <div
                            class="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-in-out dark:bg-blue-400"
                            style="width: {progress.percentage}%"
                        ></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</a>
