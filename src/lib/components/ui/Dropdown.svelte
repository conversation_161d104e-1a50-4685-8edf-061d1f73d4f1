<script lang="ts">
    import {type ComponentType, createEventDispatcher, onMount} from 'svelte';

    type optionType = {
        value: string;
        label: string;
        icon?: ComponentType;
        iconClass?: string;
        action?: () => void;
    };

    export let isOpen = false;
    export let buttonClass = '';
    export let dropdownClass = '';
    export let options: optionType[] = [];
    export let selectedOption: optionType | null = null;
    export let placeholder = '';
    export let showClearOption = false;
    export let clearOptionLabel = 'Clear';
    export let clearOptionIcon: ComponentType | null = null;

    const dispatch = createEventDispatcher();

    let dropdownButton: HTMLElement;
    let dropdownMenu: HTMLElement;

    function toggleDropdown() {
        isOpen = !isOpen;
    }

    function handleOptionClick(option: optionType) {
        if (option.action) {
            option.action();
        } else {
            dispatch('select', option);
        }
        isOpen = false;
    }

    function handleClearClick() {
        dispatch('clear');
        isOpen = false;
    }

    function handleClickOutside(event: MouseEvent) {
        if (
            dropdownButton &&
            dropdownMenu &&
            !dropdownButton.contains(event.target as Node) &&
            !dropdownMenu.contains(event.target as Node)
        ) {
            isOpen = false;
        }
    }

    onMount(() => {
        document.addEventListener('click', handleClickOutside);
        return () => {
            document.removeEventListener('click', handleClickOutside);
        };
    });
</script>

<div class="relative inline-block">
    <button
        bind:this={dropdownButton}
        type="button"
        class={buttonClass}
        on:click={toggleDropdown}
    >
        <slot name="button" selectedOption={selectedOption} placeholder={placeholder} isOpen={isOpen}>
            <div class="flex items-center space-x-2">
                {#if selectedOption && selectedOption.icon}
                    <svelte:component this={selectedOption.icon} svgClass={selectedOption.iconClass || "w-4 h-4"}/>
                    <span>{selectedOption.label}</span>
                {:else}
                    <span>{placeholder}</span>
                {/if}
            </div>
            <svg class="-mr-1 ml-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path
                    fill-rule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                />
            </svg>
        </slot>
    </button>
    {#if isOpen}
        <div
            bind:this={dropdownMenu}
            class={dropdownClass || "absolute right-0 z-[9999] mt-2 w-56 origin-top-right rounded-md bg-white shadow-xl ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-700 dark:ring-gray-600"}
        >
            <div class="py-1">
                <slot name="options" options={options} handleOptionClick={handleOptionClick}>
                    {#each options as option (option.value)}
                        <button
                            type="button"
                            class="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-600"
                            on:click={() => handleOptionClick(option)}
                        >
                            {#if option.icon}
                                <svelte:component
                                    this={option.icon}
                                    svgClass={option.iconClass || "w-4 h-4 mr-3 text-gray-600 dark:text-gray-300"}
                                />
                            {/if}
                            {option.label}
                        </button>
                    {/each}
                </slot>
                {#if showClearOption && selectedOption}
                    <hr class="my-1 border-gray-200 dark:border-gray-600"/>
                    <button
                        type="button"
                        class="flex w-full items-center px-4 py-2 text-sm text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600"
                        on:click={handleClearClick}
                    >
                        {#if clearOptionIcon}
                            <svelte:component this={clearOptionIcon} svgClass="w-4 h-4 mr-3"/>
                        {/if}
                        {clearOptionLabel}
                    </button>
                {/if}
            </div>
        </div>
    {/if}
</div>
