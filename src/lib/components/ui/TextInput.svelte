<script lang="ts">
    import type {ComponentType} from 'svelte';
    import StringService from '$lib/services/StringService';

    export let title: string;
    export let value: string;
    export let iconComponent: ComponentType | undefined = undefined;
    export let password: boolean = false;
    export let email: boolean = false;
    export let required: boolean = false;
    export let showLabel: boolean = false;
    export let error: string | undefined = undefined;

    const id = StringService.generatePseudoUniqueString('text-input');
</script>

<div class="relative w-full">
    <label for={id} class:sr-only={!showLabel}>{title}</label>
    <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
        <svelte:component this={iconComponent} svgClass="w-5 h-5 text-gray-500 dark:text-gray-400" />
    </div>
    <!-- Svelte does not allow type to be dynamic since bind:value will be different for different types. -->
    {#if password}
        <input
            id={id}
            type="password"
            class="block w-full rounded-lg border bg-white p-2 text-sm text-gray-900 placeholder-gray-500 focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
            class:border-gray-300={error === undefined}
            class:border-red-700={error !== undefined}
            class:dark:border-gray-600={error === undefined}
            class:pl-10={iconComponent !== undefined}
            class:pl-4={iconComponent === undefined}
            bind:value={value}
            placeholder={showLabel ? '' : title}
            required={required}
        />
    {:else if email}
        <input
            id={id}
            type="email"
            class="block w-full rounded-lg border bg-white p-2 text-sm text-gray-900 placeholder-gray-500 focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
            class:border-gray-300={error === undefined}
            class:border-red-700={error !== undefined}
            class:dark:border-gray-600={error === undefined}
            class:pl-10={iconComponent !== undefined}
            class:pl-4={iconComponent === undefined}
            bind:value={value}
            placeholder={showLabel ? '' : title}
            required={required}
        />
    {:else}
        <input
            id={id}
            type="text"
            class="block w-full rounded-lg border bg-white p-2 text-sm text-gray-900 placeholder-gray-500 focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
            class:border-gray-300={error === undefined}
            class:border-red-700={error !== undefined}
            class:dark:border-gray-600={error === undefined}
            class:pl-10={iconComponent !== undefined}
            class:pl-4={iconComponent === undefined}
            bind:value={value}
            placeholder={showLabel ? '' : title}
            required={required}
        />
    {/if}
    {#if error !== undefined}
        <span class="text-sm text-red-700">{error}</span>
    {/if}
</div>
