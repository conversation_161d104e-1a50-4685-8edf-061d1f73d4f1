<script lang="ts">
    import {onDestroy, onMount} from 'svelte';
    import AudiobookListItem from '$lib/components/ui/AudiobookListItem.svelte';
    import type Audiobook from '$lib/domain/Audiobook';

    export let audiobooks: Audiobook[] = [];
    export let getAudiobooks: ((page) => Promise<Audiobook[]>) | undefined = undefined;

    const activateScrollBeforePixels = 1000;
    let page = 1;
    let isScrollBeingHandled = false;

    async function onScroll() {
        if (getAudiobooks === undefined || isScrollBeingHandled) {
            return;
        }

        isScrollBeingHandled = true;

        const {scrollTop, clientHeight, scrollHeight} = document.documentElement;
        if (scrollTop + clientHeight >= scrollHeight - activateScrollBeforePixels) {
            const nextPageAudiobooks = await getAudiobooks(page);
            audiobooks = [...audiobooks, ...nextPageAudiobooks];
            page++;

            if (nextPageAudiobooks.length === 0) {
                window.removeEventListener('scroll', onScroll);
            }
        }

        isScrollBeingHandled = false;
    }

    onMount(async () => {
        if (getAudiobooks === undefined) {
            return;
        }

        audiobooks = await getAudiobooks(page);
        page++;

        window.addEventListener('scroll', onScroll);
    });

    onDestroy(() => {
        if (typeof window !== 'undefined') {
            window.removeEventListener('scroll', onScroll);
        }
    });
</script>

{#each audiobooks as audiobook, i (i)}
    <AudiobookListItem audiobook={audiobook} />
{/each}
