<script lang="ts">
    import {
        DateFormatter,
        type DateValue,
        getLocalTimeZone,
    } from "@internationalized/date";
    import CalendarIcon from "lucide-svelte/icons/calendar";
    import {Button} from "$lib/components/shadcn/ui/button";
    import {Calendar} from "$lib/components/shadcn/ui/calendar";
    import * as Popover from "$lib/components/shadcn/ui/popover";
    import {cn} from "$lib/components/shadcn/utils.js";
    import XSvg from "$lib/components/svg/XSvg.svelte";
    import {t} from '$lib/localization/Localization';

    const df = new DateFormatter("en-GB", {
        dateStyle: "long",
    });

    export let value: DateValue | undefined = undefined;
    export let showClearButton: boolean = true;

    function clearDate() {
        value = undefined;
    }
</script>

<Popover.Root openFocus>
    <Popover.Trigger asChild let:builder>
        <Button
            class={cn("justify-between w-full rounded-lg border border-gray-300 bg-white p-2.5 text-sm text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400")}
            variant="outline"
            builders={[builder]}
        >
            <div class="flex items-center">
                <CalendarIcon class="mr-2 h-4 w-4"/>
                <span>{value ? df.format(value.toDate(getLocalTimeZone())) : $t('datePicker.selectDate')}</span>
            </div>
            {#if showClearButton && value}
                <button
                    type="button"
                    class="ml-2 flex-shrink-0 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
                    aria-label="Clear date"
                    on:click|stopPropagation={clearDate}
                >
                    <XSvg svgClass="w-4 h-4" />
                </button>
            {/if}
        </Button>
    </Popover.Trigger>
    <Popover.Content class="w-auto p-0 border-gray-300 bg-white text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
        <Calendar bind:value={value} initialFocus/>
    </Popover.Content>
</Popover.Root>
