<script lang="ts">
    import {Modal} from 'flowbite';
    import type {ModalInterface} from 'flowbite/lib/esm/components/modal/interface';
    import {onMount} from 'svelte';
    import XSvg from '$lib/components/svg/XSvg.svelte';
    import StringService from '$lib/services/StringService';

    export let open = false; // this prop should always be used with bind since this component can change its value

    const id = StringService.generatePseudoUniqueString('modal');

    let modal: ModalInterface | undefined;

    onMount(() => {
        const targetEl: HTMLElement | null = document.getElementById(id);
        // PHPStorm says the following condition is always false but that's a bug since getElementById returns null if
        // it doesn't find the element
        if (targetEl === null) {
            throw new Error('Modal element not found');
        }

        modal = new Modal(targetEl, {
            onShow: () => {
                open = true;
            },
            onHide: () => {
                open = false;
            },
        });
    });

    $: open ? modal?.show() : modal?.hide();
</script>

<div
    id={id}
    class="fixed left-0 right-0 top-0 z-50 hidden h-modal w-full items-center justify-center overflow-y-auto overflow-x-hidden md:inset-0 md:h-full"
    tabindex="-1"
    aria-hidden="true"
>
    <div class="relative h-full w-full max-w-xl p-4 md:h-auto">
        <div class="relative rounded-lg bg-white p-4 shadow dark:bg-gray-800 sm:p-5">
            <div class="relative">
                <button
                    type="button"
                    class="absolute right-0 inline-flex rounded-lg bg-transparent p-1.5 text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white"
                    on:click={() => {
                        open = false;
                    }}
                >
                    <XSvg svgClass="w-5 h-5" />
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <slot />
        </div>
    </div>
</div>
