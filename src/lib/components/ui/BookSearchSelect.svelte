<script lang="ts">
    import Select from 'svelte-select';
    import DarkModeStore from '$lib/stores/DarkModeStore';

    type optionsPromise = (query: string) => Promise<string[]>;

    export let title: string;
    export let options: string[] | optionsPromise;
    export let value: string;
    export let filterText: string;
    export let onSelect: () => void;

    let listOpen: boolean = false;
    let focused: boolean = false;
    let userInteracted: boolean = false;

    function handleFocus() {
        userInteracted = true;
    }

    // this hack is needed for when the user goes back to the search page to make sure the search list is not opened
    $: if (!userInteracted && listOpen) {
        listOpen = false;
        focused = false;
    }
</script>

<!-- The purpose of this div is to enable space-y on the containing element. The div generated by the Select element has
display:contents and breaks space-y -->
<div>
    <Select
        --background={$DarkModeStore ? 'rgb(55 65 81)' : 'rgb(255 255 255)'}
        --border={$DarkModeStore ? '1px solid rgb(75 85 99)' : '1px solid rgb(209 213 219)'}
        --border-focused="1px solid rgb(59 130 246)"
        --border-hover={$DarkModeStore ? '1px solid rgb(75 85 99)' : '1px solid rgb(209 213 219)'}
        --border-radius="0.5rem"
        --clear-icon-color={$DarkModeStore ? 'rgb(255 255 255)' : 'rgb(17 24 39)'}
        --font-size="0.875rem"
        --input-color={$DarkModeStore ? 'rgb(255 255 255)' : 'rgb(17 24 39)'}
        --item-color={$DarkModeStore ? 'rgb(255 255 255)' : 'rgb(17 24 39)'}
        --item-hover-bg={$DarkModeStore ? 'rgb(75 85 99)' : 'rgb(243 244 246)'}
        --list-background={$DarkModeStore ? 'rgb(55 65 81)' : 'rgb(255 255 255)'}
        --loading-color={$DarkModeStore ? 'rgb(255 255 255)' : 'rgb(17 24 39)'}
        --placeholder-color={$DarkModeStore ? 'rgb(156 163 175)' : 'rgb(107 114 128)'}
        --selected-item-color={$DarkModeStore ? 'rgb(255 255 255)' : 'rgb(17 24 39)'}
        --width="100%"
        bind:value={value}
        bind:filterText={filterText}
        bind:listOpen={listOpen}
        bind:focused={focused}
        items={typeof options === 'function' ? undefined : options}
        loadOptions={typeof options === 'function' ? options : undefined}
        placeholder={title}
        clearFilterTextOnBlur={false}
        on:change={onSelect}
        on:focus={handleFocus}
    >
        <div slot="item" let:item>
            <img src={item.cover} class="my-2 inline-block h-16 w-16 rounded-2xl object-contain" alt="Book cover" />
            {item.label}
        </div>
        <div slot="empty"></div>
    </Select>
</div>

<style lang="css">
    :global(.svelte-select .item) {
        height: 100% !important;
    }
</style>
