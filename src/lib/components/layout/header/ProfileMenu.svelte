<script lang="ts">
    import {Dropdown} from 'flowbite';
    import {onMount} from 'svelte';
    import {goto} from '$app/navigation';
    import AuthenticationApiClient from '$lib/api/AuthenticationApiClient';
    import ArrowLeaveRectangleRight from '$lib/components/svg/ArrowLeaveRectangleRightSvg.svelte';
    import CogwheelSvg from '$lib/components/svg/CogwheelSvg.svelte';
    import LanguageSvg from '$lib/components/svg/LanguageSvg.svelte';
    import MoonSvg from '$lib/components/svg/MoonSvg.svelte';
    import SunSvg from '$lib/components/svg/SunSvg.svelte';
    import ProfilePicture from '$lib/components/ui/ProfilePicture.svelte';
    import {languages, t} from '$lib/localization/Localization';
    import AuthenticationService from '$lib/services/AuthenticationService';
    import AuthenticatedStore from '$lib/stores/AuthenticatedStore';
    import DarkModeStore from '$lib/stores/DarkModeStore';
    import LocaleStore from '$lib/stores/LocaleStore';
    import NameStore from '$lib/stores/NameStore';
    import AppRoutes from '$routes/AppRoutes';

    async function logout() {
        const authenticationApiClient = new AuthenticationApiClient();
        await authenticationApiClient.logout();

        AuthenticationService.logout();
        await goto(AppRoutes.home);
    }

    function setTheme() {
        $DarkModeStore
            ? document.documentElement.classList.add('dark')
            : document.documentElement.classList.remove('dark');
    }

    function toggleTheme() {
        DarkModeStore.toggle();
        setTheme();
    }

    const profilePictureSize = 7;
    let dropdownButton: HTMLElement;
    let dropdownMenu: HTMLElement;

    onMount(() => {
        // the library requires a new dropdown object even if you don't do anything with it, can't be helped
        // eslint-disable-next-line no-new
        new Dropdown(dropdownMenu, dropdownButton);

        setTheme();
    });
</script>

<button
    bind:this={dropdownButton}
    type="button"
    class="rounded-lg p-2 text-sm text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
>
    {#if $AuthenticatedStore}
        <ProfilePicture size={profilePictureSize} />
    {:else}
        <CogwheelSvg svgClass="h-7 w-7" />
    {/if}
</button>

<div
    bind:this={dropdownMenu}
    class="z-50 my-4 hidden w-56 list-none divide-y divide-gray-100 rounded-xl bg-gray-50 text-base shadow dark:divide-gray-600 dark:bg-gray-700"
>
    <div class="px-4 py-3">
        <span class="block text-sm font-semibold text-gray-900 dark:text-white">
            {$t('header.hello')}{#if $AuthenticatedStore}, {$NameStore}{/if}!
        </span>
    </div>

    <ul class="py-1 text-gray-700 dark:text-gray-300" aria-labelledby="profile-menu-dropdown">
        {#if $AuthenticatedStore}
            <li>
                <a
                    href={AppRoutes.accountSettings}
                    class="flex items-center px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
                >
                    <CogwheelSvg svgClass="mr-2 h-5 w-5 text-gray-400" />
                    {$t('menu.accountSettings')}
                </a>
            </li>
        {/if}
        <li>
            <button
                type="button"
                class="flex w-full items-center px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
                aria-controls="dropdown-languages"
                data-collapse-toggle="dropdown-languages"
            >
                <LanguageSvg svgClass="mr-2 h-5 w-5 text-gray-400" />
                {$t('header.language')}
            </button>
            <ul id="dropdown-languages" class="hidden">
                {#each languages as language (language.locale)}
                    <li>
                        <button
                            type="button"
                            class="flex w-full items-center px-4 py-2 pl-11 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
                            class:bg-gray-100={language.locale === $LocaleStore}
                            class:dark:bg-gray-600={language.locale === $LocaleStore}
                            class:dark:text-white={language.locale === $LocaleStore}
                            on:click={() => {
                                if ($LocaleStore !== language.locale) {
                                    $LocaleStore = language.locale;
                                }
                            }}
                        >
                            {language.name}
                        </button>
                    </li>
                {/each}
            </ul>
        </li>
        <li>
            <button
                id="theme-toggle"
                type="button"
                class="flex w-full items-center px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
                on:click={toggleTheme}
            >
                <SunSvg
                    svgId="theme-toggle-dark-icon"
                    svgClass={`mr-2 h-5 w-5 text-gray-400 ${$DarkModeStore ? 'hidden' : ''}`}
                />
                <MoonSvg
                    svgId="theme-toggle-light-icon"
                    svgClass={`mr-2 h-5 w-5 text-gray-400 ${$DarkModeStore ? '' : 'hidden'}`}
                />
                {$t('header.theme')}
            </button>
        </li>
    </ul>
    <ul class="py-1 text-gray-700 dark:text-gray-300" aria-labelledby="profile-menu-dropdown">
        {#if $AuthenticatedStore}
            <li>
                <button
                    type="button"
                    class="flex w-full items-center rounded-b-xl px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
                    on:click={logout}
                >
                    <ArrowLeaveRectangleRight svgClass="mr-2 h-5 w-5 text-gray-400" />
                    {$t('header.logout')}
                </button>
            </li>
        {/if}
    </ul>
</div>
