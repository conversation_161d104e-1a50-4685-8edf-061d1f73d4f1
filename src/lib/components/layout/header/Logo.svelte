<script lang="ts">
    import {PUBLIC_APP_NAME} from '$env/static/public';
    import LogoBlackImage from '$lib/images/LogoBlackImage.png';
    import LogoGrayImage from '$lib/images/LogoGrayImage.png';
    import LogoWhiteImage from '$lib/images/LogoWhiteImage.png';
    import DarkModeStore from '$lib/stores/DarkModeStore';
    import AppRoutes from '$routes/AppRoutes';

    let logo = LogoGrayImage;

    function hoverLogo() {
        logo = $DarkModeStore ? LogoWhiteImage : LogoBlackImage;
    }

    function normalLogo() {
        logo = LogoGrayImage;
    }
</script>

<a
    href={AppRoutes.home}
    class="flex items-center justify-center"
    data-drawer-target="drawer-navigation"
    data-drawer-hide="drawer-navigation"
    on:mouseover={hoverLogo}
    on:mouseleave={normalLogo}
    on:focus={hoverLogo}
    on:blur={normalLogo}
>
    <img src={logo} class="w-6" alt="Logo" />
    <span class="ml-3 self-center whitespace-nowrap text-2xl font-semibold dark:text-white">{PUBLIC_APP_NAME}</span>
</a>
