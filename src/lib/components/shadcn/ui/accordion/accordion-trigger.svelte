<script lang="ts">
    import {Accordion as AccordionPrimitive} from 'bits-ui';
    import ChevronDown from 'lucide-svelte/icons/chevron-down';
    import {cn} from '$lib/components/shadcn/utils.js';

    type $$Props = AccordionPrimitive.TriggerProps;
    type $$Events = AccordionPrimitive.TriggerEvents;

    let className: $$Props['class'] = undefined;
    export let level: AccordionPrimitive.HeaderProps['level'] = 3;
    export {className as class};
</script>

<AccordionPrimitive.Header class="flex" level={level}>
    <AccordionPrimitive.Trigger
        class={cn(
            'flex items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180',
            className,
        )}
        {...$$restProps}
        on:click
    >
        <slot />
        <ChevronDown class="ml-1 h-4 w-4 transition-transform duration-200" />
    </AccordionPrimitive.Trigger>
</AccordionPrimitive.Header>
