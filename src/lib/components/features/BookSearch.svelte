<script lang="ts">
    import {onMount} from 'svelte';
    import {goto} from '$app/navigation';
    import AuthorsApiClient from '$lib/api/AuthorsApiClient';
    import LanguagesApiClient from '$lib/api/LanguagesApiClient';
    import SearchApiClient from '$lib/api/SearchApiClient';
    import SubjectsApiClient from '$lib/api/SubjectsApiClient';
    import Container from '$lib/components/layout/body/Container.svelte';
    import * as Accordion from '$lib/components/shadcn/ui/accordion/index.ts';
    import BookListUI from '$lib/components/ui/BookListUI.svelte';
    import BookSearchSelect from '$lib/components/ui/BookSearchSelect.svelte';
    import Button from '$lib/components/ui/Button.svelte';
    import NumberRange from '$lib/components/ui/NumberRange.svelte';
    import Select from '$lib/components/ui/Select.svelte';
    import TextInput from '$lib/components/ui/TextInput.svelte';
    import ToggleButton from '$lib/components/ui/ToggleButton.svelte';
    import type Language from '$lib/domain/Language';
    import type SearchBook from '$lib/domain/SearchBook';
    import {t} from '$lib/localization/Localization';
    import BookSearchFiltersStores from '$lib/stores/BookSearchFiltersStores';
    import AppRoutes from '$routes/AppRoutes';

    const minPossiblePublishYear = 0;
    const maxPossiblePublishYear = new Date().getFullYear();
    const booksPerRequest = 20;

    const {title, author, subject, isbn, publishYearFrom, publishYearTo, language, onlyFree} = BookSearchFiltersStores;

    let languages: Language[] = [];
    let key = 1;
    let accordionValue = [];

    async function getBooks(query: string): Promise<object[]> {
        if (query === '') {
            return [];
        }

        const searchClient = new SearchApiClient();
        const response = await searchClient.books({
            title: query,
        });

        return response.ok
            ? response.data.hits.map((book) => {
                  return {
                      id: book.id,
                      label: book.title,
                      cover: book.cover,
                  };
              })
            : [];
    }

    async function getAuthors(query: string): Promise<string[]> {
        if (query === '') {
            return [];
        }

        const authorsApiClient = new AuthorsApiClient();
        const response = await authorsApiClient.index(query);

        return response.data;
    }

    async function getSubjects(query: string): Promise<string[]> {
        if (query === '') {
            return [];
        }

        const subjectsApiClient = new SubjectsApiClient();
        const response = await subjectsApiClient.index(query);

        return response.data;
    }

    async function getLanguages(): Promise<void> {
        const languagesApiClient = new LanguagesApiClient();
        const response = await languagesApiClient.index();

        if (!response.ok) {
            return;
        }

        languages = response.data.map((language) => {
            language.label = language.text;

            return language;
        });
    }

    function isDefaultSearch() {
        return (
            $title === '' &&
            ($author === null || $author === undefined) &&
            ($subject === null || $subject === undefined) &&
            $isbn === '' &&
            $publishYearFrom === null &&
            $publishYearTo === null &&
            $language === null
        );
    }

    async function searchBooks(page: number = 1): Promise<SearchBook[]> {
        if (page < 1) {
            throw new Error('Books page cannot be lower than 1.');
        }

        const searchClient = new SearchApiClient();
        const response = await searchClient.books({
            title: $title || undefined,
            author: $author?.label || undefined,
            subject: $subject?.label || undefined,
            isbn: $isbn || undefined,
            onlyFree: $onlyFree,
            minPublishYear: $publishYearFrom || undefined,
            maxPublishYear: $publishYearTo || undefined,
            language: $language?.value || undefined,
            offset: (page - 1) * booksPerRequest,
            limit: booksPerRequest,
        });

        return response.ok ? response.data.hits : [];
    }

    function search() {
        key++;
    }

    $: {
        $onlyFree = $onlyFree;
        search();
    }

    onMount(() => {
        getLanguages(); // this call could be cached since languages mostly stay the same

        if (!isDefaultSearch()) {
            openAdvancedFilters();
        }
    });

    function handleKeydown(event: KeyboardEvent) {
        if (event.key !== 'Enter') {
            return;
        }

        event.preventDefault();
        event.stopPropagation();

        (event.target as HTMLElement).blur();

        const form = (event.target as HTMLElement).closest('form');
        if (form) {
            form.dispatchEvent(new Event('submit', {cancelable: true}));
        }
    }

    function openAdvancedFilters() {
        accordionValue = ['item-1'];
    }
</script>

<Container>
    <div class="flex flex-col items-center justify-between md:flex-row">
        <div class="mx-auto mb-5 w-full lg:w-1/2">
            <div class="mb-8">{$t('search.tagline')}</div>
            <form class="space-y-3" on:submit|preventDefault={search} on:keydown|capture={handleKeydown}>
                <BookSearchSelect
                    title={$t('search.title')}
                    bind:filterText={$title}
                    options={getBooks}
                    onSelect={async (event) => {
                        await goto(AppRoutes.book(event.detail.id));
                    }}
                />
                <Accordion.Root bind:value={accordionValue}>
                    <Accordion.Item value="item-1">
                        <Accordion.Trigger class="text-blue-600 dark:text-blue-400">
                            {$t('search.advancedFilters')}
                        </Accordion.Trigger>
                        <Accordion.Content>
                            <div class="space-y-3">
                                <Select title={$t('search.author')} bind:value={$author} options={getAuthors} />
                                <Select title={$t('search.subject')} bind:value={$subject} options={getSubjects} />
                                <TextInput title={$t('search.isbn')} bind:value={$isbn} />
                                <NumberRange
                                    title={$t('search.publishYear')}
                                    bind:valueFrom={$publishYearFrom}
                                    bind:valueTo={$publishYearTo}
                                    fromMin={minPossiblePublishYear}
                                    fromMax={maxPossiblePublishYear}
                                    toMin={minPossiblePublishYear}
                                    toMax={maxPossiblePublishYear}
                                />
                                <Select title={$t('search.language')} bind:value={$language} options={languages} />
                                <ToggleButton title={$t('search.freeBooks')} bind:checked={$onlyFree} />
                                <Button title={$t('search.search')} />
                            </div>
                        </Accordion.Content>
                    </Accordion.Item>
                </Accordion.Root>
            </form>
        </div>
    </div>
    {#key key}
        {#if isDefaultSearch()}
            <div class="mb-5 font-bold">{$t('search.defaultSearchTitle')}</div>
        {/if}
        <BookListUI getBooks={searchBooks} />
    {/key}
</Container>
