<script lang="ts">
    import Container from '$lib/components/layout/body/Container.svelte';
    import BookList<PERSON> from '$lib/components/ui/BookListUI.svelte';
    import BookListItemCurrentlyReading from '$lib/components/ui/BookListItemCurrentlyReading.svelte';
    import ReadingActivityModal from '$lib/components/ui/ReadingActivityModal.svelte';
    import type Book from '$lib/domain/Book';
    import type ReadingActivity from '$lib/domain/ReadingActivity';
    import type Recommendations from '$lib/domain/Recommendations';
    import {t} from '$lib/localization/Localization';
    import AppRoutes from '$routes/AppRoutes';

    export let recommendations: Recommendations;

    // Modal state
    let showModal = false;
    let selectedBook: Book | null = null;
    let selectedActivity: ReadingActivity | null = null;

    function handleEditActivity(event: CustomEvent<{ book: Book; activity: ReadingActivity | null }>) {
        selectedBook = event.detail.book;
        selectedActivity = event.detail.activity;
        showModal = true;
    }
</script>

{#if recommendations.currentlyReading.length > 0}
    <Container title={$t('recommendations.continueReading')}>
        <div class="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4">
            {#each recommendations.currentlyReading as book, i (i)}
                <BookListItemCurrentlyReading {book} />
            {/each}
        </div>
    </Container>
{/if}
{#if recommendations.top.length > 0}
    <h1 class="mb-3 mt-6 text-center text-2xl font-bold">{$t('recommendations.topThreeRecommendations')}</h1>
    <div class="container mx-auto flex flex-wrap justify-center">
        {#each recommendations.top as book, index (index)}
            <div class="inline-block p-2 text-center lg:w-1/3">
                <a href={AppRoutes.book(book.uuid)}>
                    <img src={book.cover} class="mx-auto h-64 w-full rounded-2xl object-contain" alt="Book cover" />
                    <div>{book.title}</div>
                </a>
            </div>
        {/each}
    </div>
{/if}
{#if recommendations.likes.length > 0}
    <Container title={$t('recommendations.moreBooksLikes')}>
        <BookListUI books={recommendations.likes} />
    </Container>
{/if}
{#if recommendations.likes_free.length > 0}
    <Container title={$t('recommendations.freeBooksLikes')}>
        <BookListUI books={recommendations.likes_free} />
    </Container>
{/if}
{#if recommendations.authors.length > 0}
    <Container title={$t('recommendations.moreBooksAuthors')}>
        <BookListUI books={recommendations.authors} />
    </Container>
{/if}
{#if recommendations.subjects.length > 0}
    <Container title={$t('recommendations.moreBooksSubjects')}>
        <BookListUI books={recommendations.subjects} />
    </Container>
{/if}
