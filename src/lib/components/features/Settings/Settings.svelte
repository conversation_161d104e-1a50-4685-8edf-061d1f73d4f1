<script lang="ts">
    import AccountSettings from '$lib/components/features/Settings/AccountSettings.svelte';
    import ChangePassword from '$lib/components/features/Settings/ChangePassword.svelte';
    import ChangeProfilePicture from '$lib/components/features/Settings/ChangeProfilePicture.svelte';
    import HalfWidthContainer from '$lib/components/layout/body/HalfWidthContainer.svelte';
    import type Settings from '$lib/domain/Settings';

    export let setting: Settings;
</script>

<HalfWidthContainer>
    <AccountSettings setting={setting} />
    <ChangeProfilePicture />
    {#if setting.isPasswordSet}
        <ChangePassword />
    {/if}
</HalfWidthContainer>
