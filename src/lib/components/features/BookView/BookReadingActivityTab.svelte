<script lang="ts">
    import {CalendarDate, type DateValue, getLocalTimeZone, today} from '@internationalized/date';
    import {onMount} from 'svelte';
    import LanguagesApiClient from '$lib/api/LanguagesApiClient';
    import type {CreateReadingActivityData, UpdateReadingActivityData} from '$lib/api/ReadingActivitiesApiClient';
    import ReadingActivitiesApiClient from '$lib/api/ReadingActivitiesApiClient';
    import SpinnerSvg from '$lib/components/svg/SpinnerSvg.svelte';
    import Button from '$lib/components/ui/Button.svelte';
    import DatePicker from '$lib/components/ui/DatePicker.svelte';
    import Modal from '$lib/components/ui/Modal.svelte';
    import NumberInput from '$lib/components/ui/NumberInput.svelte';
    import Select from '$lib/components/ui/Select.svelte';
    import type Book from '$lib/domain/Book';
    import type Language from '$lib/domain/Language';
    import type ReadingActivity from '$lib/domain/ReadingActivity';
    import {t} from '$lib/localization/Localization';
    import ToastNotificationsStore from '$lib/stores/ToastNotificationsStore';

    export let book: Book;

    let activities: ReadingActivity[] = [];
    let loading = true;
    let showFormModal = false;
    let showDeleteModal = false;
    let editingActivity: ReadingActivity | null = null;
    let deletingActivity: ReadingActivity | null = null;
    let isEditing = false;
    let modalTriggeredByStatusChange = false;

    let selectedLanguage: Language | null = null;
    let startDate: DateValue | undefined = today(getLocalTimeZone());
    let endDate: DateValue | undefined;
    let pagesRead = '';
    let totalPages = '';

    let validationErrors: Record<string, string> = {};

    let languageOptions: Language[] = [];

    onMount(() => {
        loadLanguages();
        loadActivities();
        setupLanguageOptions();
    });

    async function loadLanguages() {
        const languagesApiClient = new LanguagesApiClient();
        const response = await languagesApiClient.index();

        if (!response.ok) {
            ToastNotificationsStore.push({
                text: $t('notifications.genericError'),
                type: 'warning'
            });

            return;
        }

        languageOptions = response.data.map((language) => ({
            ...language,
            label: language.text
        }));
    }

    function setupLanguageOptions() {
        if (book.languages.length > 0) {
            selectedLanguage = book.languages[0];
            selectedLanguage.label = selectedLanguage.text;
        }

        if (book.numberOfPages) {
            totalPages = book.numberOfPages.toString();
        }
    }

    async function loadActivities() {
        loading = true;
        const apiClient = new ReadingActivitiesApiClient();
        const response = await apiClient.index(book.uuid);

        loading = false;

        if (!response.ok) {
            ToastNotificationsStore.push({
                text: $t('notifications.genericError'),
                type: 'warning'
            });

            return;
        }

        activities = response.data;
    }

    function openAddModal() {
        isEditing = false;
        editingActivity = null;
        modalTriggeredByStatusChange = false;
        resetForm();
        showFormModal = true;
    }

    export function openAddModalFromExternal(triggeredByStatusChange: boolean = false) {
        isEditing = false;
        editingActivity = null;
        modalTriggeredByStatusChange = triggeredByStatusChange;
        resetForm();
        showFormModal = true;
    }

    function openEditModal(activity: ReadingActivity) {
        isEditing = true;
        editingActivity = activity;
        modalTriggeredByStatusChange = false;
        populateFormFromActivity(activity);
        showFormModal = true;
    }

    function openDeleteModal(activity: ReadingActivity) {
        deletingActivity = activity;
        showDeleteModal = true;
    }

    function resetForm() {
        selectedLanguage = book.languages.length > 0 ? book.languages[0] : null;
        if (selectedLanguage !== null) {
            selectedLanguage.label = selectedLanguage.text;
        }

        startDate = today(getLocalTimeZone());
        endDate = undefined;
        pagesRead = '';
        totalPages = book.numberOfPages ? book.numberOfPages.toString() : '';
        validationErrors = {};
    }

    function populateFormFromActivity(activity: ReadingActivity) {
        selectedLanguage = activity.language || null;
        if (selectedLanguage !== null) {
            selectedLanguage.label = selectedLanguage.text;
        }

        if (activity.startDate) {
            const startDateObj = new Date(activity.startDate);
            startDate = new CalendarDate(startDateObj.getFullYear(), startDateObj.getMonth() + 1, startDateObj.getDate());
        } else {
            startDate = today(getLocalTimeZone());
        }

        if (activity.endDate) {
            const endDateObj = new Date(activity.endDate);
            endDate = new CalendarDate(endDateObj.getFullYear(), endDateObj.getMonth() + 1, endDateObj.getDate());
        } else {
            endDate = undefined;
        }

        pagesRead = activity.pagesRead ? activity.pagesRead.toString() : '';
        totalPages = activity.totalPages ? activity.totalPages.toString() : '';
        validationErrors = {};
    }

    function validateForm(): boolean {
        validationErrors = {};

        if (endDate && startDate && endDate.compare(startDate) < 0) {
            validationErrors.endDate = $t('readingActivity.validationEndDateAfterStart');
        }

        const pagesReadNum = parseInt(pagesRead);
        const totalPagesNum = parseInt(totalPages);
        if (pagesRead && totalPages && !isNaN(pagesReadNum) && !isNaN(totalPagesNum) && pagesReadNum > totalPagesNum) {
            validationErrors.pagesRead = $t('readingActivity.validationPagesReadExceedsTotal');
        }

        return Object.keys(validationErrors).length === 0;
    }

    async function saveActivity() {
        if (!validateForm()) {
            return;
        }

        const apiClient = new ReadingActivitiesApiClient();

        const activityData: CreateReadingActivityData | UpdateReadingActivityData = {
            languageCode: selectedLanguage?.value || undefined,
            startDate: startDate ? `${startDate.year}-${startDate.month.toString().padStart(2, '0')}-${startDate.day.toString().padStart(2, '0')}` : undefined,
            endDate: endDate ? `${endDate.year}-${endDate.month.toString().padStart(2, '0')}-${endDate.day.toString().padStart(2, '0')}` : undefined,
            pagesRead: pagesRead ? parseInt(pagesRead) : undefined,
            totalPages: totalPages ? parseInt(totalPages) : undefined,
        };

        let response;
        if (editingActivity) {
            response = await apiClient.update(editingActivity.id, activityData);
        } else {
            response = await apiClient.store({
                bookId: book.uuid,
                ...activityData
            });
        }

        if (!response.ok) {
            ToastNotificationsStore.push({
                text: $t('notifications.genericError'),
                type: 'warning'
            });

            return;
        }

        showFormModal = false;
        editingActivity = null;
        isEditing = false;
        await loadActivities();
    }

    async function deleteActivity() {
        if (!deletingActivity) {
            return;
        }

        const apiClient = new ReadingActivitiesApiClient();
        const response = await apiClient.destroy(deletingActivity.id);

        if (response.ok) {
            showDeleteModal = false;
            deletingActivity = null;
            await loadActivities();
        } else {
            ToastNotificationsStore.push({
                text: $t('notifications.genericError'),
                type: 'warning'
            });
        }
    }

    function formatDate(dateString: string | null): string {
        if (!dateString) {
            return '';
        }
        return new Date(dateString).toLocaleDateString('en-GB');
    }

    function getLanguageName(language: Language | null): string {
        return language?.text || '-';
    }
</script>

<div class="space-y-6">
    <div class="flex justify-between items-center">
        <Button title={$t('readingActivity.addActivity')} callback={openAddModal}/>
    </div>
    {#if loading}
        <div class="flex justify-center py-8">
            <SpinnerSvg svgClass="w-8 h-8"/>
        </div>
    {:else if activities.length === 0}
        <div class="text-center py-8 text-gray-500 dark:text-gray-400">
            {$t('readingActivity.noActivities')}
        </div>
    {:else}
        <div class="space-y-4">
            {#each activities as activity (activity.id)}
                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div class="flex justify-between items-start">
                        <div class="space-y-2">
                            <div class="flex items-center space-x-4">
                                <span class="font-medium">{formatDate(activity.startDate)}
                                    - {formatDate(activity.endDate)}</span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">{getLanguageName(activity.language)}</span>
                            </div>
                            <div class="text-sm">
                                {$t('readingActivity.pages')}: {activity.pagesRead || 0} / {activity.totalPages || 0}
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <Button
                                title={$t('readingActivity.edit')}
                                callback={() => openEditModal(activity)}
                                primary={true}
                                extraClass="text-xs px-2 py-1 text-white"
                            />
                            <Button
                                title={$t('readingActivity.delete')}
                                callback={() => openDeleteModal(activity)}
                                primary={true}
                                extraClass="text-xs px-2 py-1 bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700"
                            />
                        </div>
                    </div>
                </div>
            {/each}
        </div>
    {/if}
</div>
<Modal bind:open={showFormModal}>
    <div class="mb-4 flex justify-between rounded-t sm:mb-5">
        <div class="text-lg text-gray-900 dark:text-white md:text-xl">
            <h3 class="font-semibold">
                {isEditing ? $t('readingActivity.editActivity') : $t('readingActivity.addActivity')}
            </h3>
        </div>
    </div>
    <form class="space-y-4" on:submit|preventDefault={saveActivity}>
        <div class="text-sm">{$t('readingActivity.description')}</div>
        <div class="grid grid-cols-2 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {$t('readingActivity.startDate')}
                </label>
                <div class="w-full">
                    <DatePicker bind:value={startDate}/>
                </div>
                {#if validationErrors.startDate}
                    <p class="text-red-600 text-sm mt-1">{validationErrors.startDate}</p>
                {/if}
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {$t('readingActivity.endDate')}
                </label>
                <div class="w-full">
                    <DatePicker bind:value={endDate}/>
                </div>
                {#if validationErrors.endDate}
                    <p class="text-red-600 text-sm mt-1">{validationErrors.endDate}</p>
                {/if}
            </div>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {$t('readingActivity.language')}
            </label>
            <div>
                <Select title={$t('readingActivity.language')} bind:value={selectedLanguage} options={languageOptions}/>
            </div>
        </div>
        <div class="grid grid-cols-2 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {$t('readingActivity.pagesRead')}
                </label>
                <NumberInput title={$t('readingActivity.pagesRead')} bind:value={pagesRead} min={0}/>
                {#if validationErrors.pagesRead}
                    <p class="text-red-600 text-sm mt-1">{validationErrors.pagesRead}</p>
                {/if}
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {$t('readingActivity.totalPages')}
                </label>
                <NumberInput title={$t('readingActivity.totalPages')} bind:value={totalPages} min={1}/>
                {#if validationErrors.totalPages}
                    <p class="text-red-600 text-sm mt-1">{validationErrors.totalPages}</p>
                {/if}
            </div>
        </div>
        <div class="flex justify-end space-x-2 pt-4">
            <Button
                title={modalTriggeredByStatusChange ? "Skip" : "Cancel"}
                callback={() => {
                    showFormModal = false;
                    editingActivity = null;
                    isEditing = false;
                    modalTriggeredByStatusChange = false;
                }}
                primary={false}
            />
            <Button title={$t('readingActivity.save')}/>
        </div>
    </form>
</Modal>
<Modal bind:open={showDeleteModal}>
    <div class="mb-4 flex justify-between rounded-t sm:mb-5">
        <div class="text-lg text-gray-900 dark:text-white md:text-xl">
            <h3 class="font-semibold">{$t('readingActivity.confirmDelete')}</h3>
        </div>
    </div>
    <div class="space-y-4">
        <p class="text-gray-700 dark:text-gray-300">
            {$t('readingActivity.confirmDeleteMessage')}
        </p>
        <div class="flex justify-end space-x-2 pt-4">
            <Button
                title="Cancel"
                callback={() => { showDeleteModal = false; deletingActivity = null; }}
                primary={false}
            />
            <Button
                title={$t('readingActivity.delete')}
                callback={deleteActivity}
                extraClass="bg-red-600 dark:bg-red-600 text-white dark:text-white hover:bg-red-700 dark:hover:bg-red-700"
            />
        </div>
    </div>
</Modal>
