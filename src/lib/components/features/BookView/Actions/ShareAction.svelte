<script lang="ts">
    import {type ComponentType} from 'svelte';
    import {page} from '$app/stores';
    import BooksApiClient from '$lib/api/BooksApiClient';
    import FacebookSvg from '$lib/components/svg/FacebookSvg.svelte';
    import PinterestSvg from '$lib/components/svg/PinterestSvg.svelte';
    import ShareSvg from '$lib/components/svg/ShareSvg.svelte';
    import XTheCompanySvg from '$lib/components/svg/XTheCompanySvg.svelte';
    import Dropdown from '$lib/components/ui/Dropdown.svelte';
    import type Book from '$lib/domain/Book';

    export let book: Book;

    type ShareOption = {
        value: string;
        label: string;
        icon: ComponentType;
        action: () => void;
    };

    const shareOptions: ShareOption[] = [
        {
            value: 'facebook',
            label: 'Facebook',
            icon: FacebookSvg,
            action: () => shareOnFacebook(),
        },
        {
            value: 'pinterest',
            label: 'Pinterest',
            icon: PinterestSvg,
            action: () => shareOnPinterest(),
        },
         {
            value: 'x',
            label: 'X',
            icon: XTheCompanySvg,
            action: () => shareOnX(),
        },
    ];

    function shareOnX() {
        let query = `text=${encodeURIComponent(book.title)}&url=${encodeURIComponent($page.url.href)}&via=LiberomApp`;
        const hashtags = ['books', 'reading'];
        if (hashtags.length > 0) {
            query += `&hashtags=${hashtags.map((hashtag) => encodeURIComponent(hashtag)).join(',')}`;
        }

        const popupWidth = 600;
        const popupHeight = 400;
        const popupX = (screen.width - popupWidth) / 2;
        const popupY = (screen.height - popupHeight) / 2;

        window.open(
            `https://x.com/intent/post?${query}`,
            '_blank',
            `width=${popupWidth},height=${popupHeight},left=${popupX},top=${popupY}`,
        );

        const booksApiClient = new BooksApiClient();
        booksApiClient.share(book.uuid, 'x');
    }

    function shareOnFacebook() {
        const popupWidth = 600;
        const popupHeight = 400;
        const popupX = (screen.width - popupWidth) / 2;
        const popupY = (screen.height - popupHeight) / 2;

        window.open(
            `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent($page.url.href)}`,
            '_blank',
            `width=${popupWidth},height=${popupHeight},left=${popupX},top=${popupY}`,
        );

        const booksApiClient = new BooksApiClient();
        booksApiClient.share(book.uuid, 'facebook');
    }

    function shareOnPinterest() {
        const popupWidth = 600;
        const popupHeight = 400;
        const popupX = (screen.width - popupWidth) / 2;
        const popupY = (screen.height - popupHeight) / 2;

        window.open(
            `https://pinterest.com/pin/create/button/?url=${encodeURIComponent($page.url.href)}`,
            '_blank',
            `width=${popupWidth},height=${popupHeight},left=${popupX},top=${popupY}`,
        );

        const booksApiClient = new BooksApiClient();
        booksApiClient.share(book.uuid, 'pinterest');
    }
</script>

<Dropdown
    options={shareOptions}
    buttonClass="inline-flex items-center rounded-xl bg-gray-600 p-1.5 text-center align-middle text-sm font-medium text-white hover:bg-gray-700 focus:outline-none  dark:bg-gray-600 dark:hover:bg-gray-700"
    dropdownClass="absolute left-0 z-[9999] mt-2 w-48 origin-top-left rounded-md bg-white shadow-xl ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-700 dark:ring-gray-600"
>
    <svelte:fragment slot="button">
        <ShareSvg svgClass="w-5 h-5" />
    </svelte:fragment>
</Dropdown>
