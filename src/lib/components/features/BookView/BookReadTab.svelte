<script lang="ts">
    import {onMount} from 'svelte';
    import BooksApiClient from '$lib/api/BooksApiClient';
    import SpinnerSvg from '$lib/components/svg/SpinnerSvg.svelte';
    import type Book from '$lib/domain/Book';
    import DOMService from '$lib/services/DOMService';
    import LocaleStore from '$lib/stores/LocaleStore';

    export let book: Book;

    let readerContainer: HTMLFormElement;

    function loadGoogleBooksReader() {
        const viewer = new window.google.books.DefaultViewer(readerContainer);
        viewer.load(book.urlIdentifier.key, freeBookNotFound);
    }

    function freeBookNotFound() {
        const booksApiClient = new BooksApiClient();
        booksApiClient.setNotFree(book.urlIdentifier.key);
    }

    onMount(() => {
        if (window.google?.books?.DefaultViewer === undefined) {
            DOMService.loadScript(`https://www.google.com/books/api.js?hl=${$LocaleStore}`, loadGoogleBooksReader);

            return;
        }

        loadGoogleBooksReader();
    });
</script>

<div bind:this={readerContainer} class="h-[calc(80vh)] w-full">
    <SpinnerSvg svgClass="w-8 h-8 mx-auto" />
</div>
