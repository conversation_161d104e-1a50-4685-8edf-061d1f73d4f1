<script lang="ts">
    import {error} from '@sveltejs/kit';
    import {PUBLIC_API_URL} from '$env/static/public';
    import BooksApiClient from '$lib/api/BooksApiClient';
    import SpeakerWaveSvg from '$lib/components/svg/SpeakerWaveSvg.svelte';
    import SpinnerSvg from '$lib/components/svg/SpinnerSvg.svelte';
    import Button from '$lib/components/ui/Button.svelte';
    import type Book from '$lib/domain/Book';
    import type BookSummary from '$lib/domain/BookSummary';
    import {t} from '$lib/localization/Localization';
    import AudioPlayerStores from '$lib/stores/AudioPlayerStores';
    import LocaleStore from '$lib/stores/LocaleStore';
    import StatusCodes from '$routes/StatusCodes';

    export let book: Book;

    const isOpenStore = AudioPlayerStores.isOpen;
    const isPlayingStore = AudioPlayerStores.isPlaying;
    const chapterIndexStore = AudioPlayerStores.chapterIndex;
    const playlistStore = AudioPlayerStores.playlist;

    async function getBookSummary(): Promise<BookSummary> {
        const booksApiClient = new BooksApiClient();
        const response = await booksApiClient.summary(book.uuid);

        if (!response.ok) {
            throw error(StatusCodes.clientError.notFound, 'Not found');
        }

        return response.data;
    }

    function listen() {
        $playlistStore = [
            {
                name: book.title,
                url: `${PUBLIC_API_URL}/books/${book.uuid}/summary-tts/${$LocaleStore}`,
            },
        ];
        $chapterIndexStore = 0;
        $isOpenStore = true;
        $isPlayingStore = true;
    }
</script>

<div class="space-y-6">
    {#await getBookSummary()}
        <SpinnerSvg svgClass="w-8 h-8 mx-auto" />
    {:then bookSummary}
        <div class="mb-5">
            <Button title={$t('book.listen')} icon={SpeakerWaveSvg} callback={listen} />
        </div>
        <div>
            {@html bookSummary.summary}
        </div>
    {/await}
</div>
