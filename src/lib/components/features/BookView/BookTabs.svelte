<script lang="ts">
    import {tick} from "svelte";
    import {goto} from '$app/navigation';
    import type Book from '$lib/domain/Book';
    import {t} from '$lib/localization/Localization';
    import AuthenticatedStore from '$lib/stores/AuthenticatedStore';
    import AppRoutes from '$routes/AppRoutes';
    import BookDetailsTab from './BookDetailsTab.svelte';
    import BookReadingActivityTab from './BookReadingActivityTab.svelte';
    import BookReadTab from './BookReadTab.svelte';
    import BookSummaryTab from './BookSummaryTab.svelte';

    export let book: Book;

    let activeTab = 'details';
    let visitedTabs = new Set(['details']);
    let readingActivityTabComponent: BookReadingActivityTab;

    $: tabs = [
        {id: 'details', label: $t('book.details'), component: BookDetailsTab},
        {id: 'summary', label: $t('book.summary'), component: BookSummaryTab},
        {id: 'read', label: $t('book.read'), component: BookReadTab, condition: book.isFree},
        {id: 'reading-activity', label: $t('book.readingActivity'), component: BookReadingActivityTab}
    ];

    function setActiveTab(tabId: string) {
        if ((tabId === 'summary' || tabId === 'read') && !$AuthenticatedStore) {
            goto(AppRoutes.login);
            return;
        }

        activeTab = tabId;
        visitedTabs.add(tabId);
        visitedTabs = visitedTabs; // trigger reactivity
    }

    export async function openReadingActivityModal(triggeredByStatusChange: boolean = false) {
        setActiveTab('reading-activity');
        await tick();

        if (readingActivityTabComponent) {
            readingActivityTabComponent.openAddModalFromExternal(triggeredByStatusChange);
        }
    }
</script>

<div class="w-full">
    <div class="border-b border-gray-200 dark:border-gray-700">
        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
            {#each tabs as tab (tab.id)}
                {#if tab.condition === undefined || tab.condition}
                    <button
                        type="button"
                        class="whitespace-nowrap border-b-2 py-2 px-1 text-sm font-medium transition-colors duration-200 {activeTab === tab.id
                            ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                            : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'}"
                        aria-current={activeTab === tab.id ? 'page' : undefined}
                        on:click={() => setActiveTab(tab.id)}
                    >
                        {tab.label}
                    </button>
                {/if}
            {/each}
        </nav>
    </div>
    <div class="mt-6">
        {#each tabs as tab (tab.id)}
            {#if tab.condition === undefined || tab.condition}
                <div
                    class="tab-content"
                    class:block={activeTab === tab.id}
                    class:hidden={activeTab !== tab.id}
                    role="tabpanel"
                >
                    {#if visitedTabs.has(tab.id)}
                        {#if tab.id === 'reading-activity'}
                            <svelte:component this={tab.component} bind:this={readingActivityTabComponent} book={book}/>
                        {:else}
                            <svelte:component this={tab.component} book={book}/>
                        {/if}
                    {/if}
                </div>
            {/if}
        {/each}
    </div>
</div>
