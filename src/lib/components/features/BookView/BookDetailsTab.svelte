<script lang="ts">
    import type Book from '$lib/domain/Book';
    import {t} from '$lib/localization/Localization';
    import BookSearchFiltersService from '$lib/services/BookSearchFiltersService';
    import StringService from '$lib/services/StringService';
    import AppRoutes from '$routes/AppRoutes';

    export let book: Book;

    const identifierLabels = {
        isbn10: 'ISBN 10',
        ISBN10: 'ISBN 10',
        isbn13: 'ISBN 13',
        ISBN13: 'ISBN 13',
    };
</script>

<div class="space-y-6">
    <div>
        <h1 class="text-3xl font-bold mb-2">{book.title}</h1>
        <div class="mb-4">
            <span>{$t('book.by')}</span>
            {#each book.authors as author, index (index)}
                <a href={AppRoutes.author(book.authorIDs[index])}>{author}</a>
                {#if index < book.authors.length - 1}
                    <span>, </span>
                {/if}
            {/each}
        </div>
    </div>
    <div>
        {#if book.description}
            {StringService.stripTags(book.description)}
        {/if}
    </div>
    <div class="space-y-1">
        {#if book.publishDates.length > 0}
            <div>
                <span class="font-semibold">{$t('book.publishDate')}:</span>
                <span>{book.publishDates.join(', ')}</span>
            </div>
        {/if}
        {#if book.numberOfPages}
            <div>
                <span class="font-semibold">{$t('book.numberOfPages')}:</span>
                <span>{book.numberOfPages}</span>
            </div>
        {/if}
        {#if book.subjects.length > 0}
            <div>
                <span class="font-semibold">{$t('book.subjects')}:</span>
                {#each book.subjects as subject, index (index)}
                    <button type="button" on:click={() => BookSearchFiltersService.goToSubjectSearch(subject)}>
                        {subject}
                    </button>
                    {#if index < book.subjects.length - 1}
                        <span>, </span>
                    {/if}
                {/each}
            </div>
        {/if}
        {#if book.languages.length > 0}
            <div>
                <span class="font-semibold">{$t('book.languages')}:</span>
                {#each book.languages as language, index (index)}
                    <button type="button" on:click={() => BookSearchFiltersService.goToLanguageSearch(language)}>
                        {language.text}
                    </button>
                    {#if index < book.languages.length - 1}
                        <span>, </span>
                    {/if}
                {/each}
            </div>
        {/if}
        {#each Object.entries(book.identifiers) as [identifierType, identifier] (identifierType)}
            <div>
                <span class="font-semibold">{identifierLabels[identifierType] || identifierType}:</span>
                <button type="button" on:click={() => BookSearchFiltersService.goToIsbnSearch(identifier)}>
                    {identifier}
                </button>
            </div>
        {/each}
    </div>
</div>
