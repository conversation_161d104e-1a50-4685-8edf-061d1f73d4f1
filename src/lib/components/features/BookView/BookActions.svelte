<script lang="ts">
    import BookListAction from './Actions/BookListAction.svelte';
    import LikeAction from './Actions/LikeAction.svelte';
    import ReadStatusAction from './Actions/ReadStatusAction.svelte';
    import ShareAction from './Actions/ShareAction.svelte';
    import type Book from '$lib/domain/Book';
    import type BookTabs from './BookTabs.svelte';

    export let book: Book;
    export let bookTabsComponent: BookTabs | undefined = undefined;
</script>

<div class="mt-6 space-y-5">
    <div class="flex items-center justify-between">
        <ShareAction book={book} />
        <LikeAction book={book} />
    </div>
    <ReadStatusAction book={book} bookTabsComponent={bookTabsComponent} />
    <BookListAction book={book} />
</div>
