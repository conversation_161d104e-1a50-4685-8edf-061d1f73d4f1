<script lang="ts">
    import {onDestroy, onMount} from 'svelte';
    import BackwardStepSvg from '$lib/components/svg/BackwardStepSvg.svelte';
    import ForwardStepSvg from '$lib/components/svg/ForwardStepSvg.svelte';
    import PauseCircleSvg from '$lib/components/svg/PauseCircleSvg.svelte';
    import PlayCircleSvg from '$lib/components/svg/PlayCircleSvg.svelte';
    import VolumeMutedSvg from '$lib/components/svg/VolumeMutedSvg.svelte';
    import VolumeSvg from '$lib/components/svg/VolumeSvg.svelte';
    import XSvg from '$lib/components/svg/XSvg.svelte';
    import AudioPlayerStores from '$lib/stores/AudioPlayerStores';

    const audio = new Audio();
    let isMuted = false;
    let volume = 0.5;
    let currentTime = 0;
    let duration = 0;
    let playbackRate = 1.0;
    let showRates = false;
    const playbackRates = [0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0];
    const isOpenStore = AudioPlayerStores.isOpen;
    const isPlayingStore = AudioPlayerStores.isPlaying;
    const chapterIndexStore = AudioPlayerStores.chapterIndex;
    const playlistStore = AudioPlayerStores.playlist;

    $: loadTrack($chapterIndexStore, $playlistStore);
    $: $isPlayingStore ? audio.play() : audio.pause();

    function handleSpacebar(event) {
        if (event.code === 'Space') {
            event.preventDefault();
            togglePlay();
        }
    }

    function loadTrack(index, playlist) {
        if (playlist.length === 0) {
            return;
        }

        audio.src = playlist[index].url;
        audio.volume = isMuted ? 0 : volume;
        audio.playbackRate = playbackRate;

        $isPlayingStore = true;
    }

    function togglePlay() {
        $isPlayingStore = !$isPlayingStore;
    }

    function changeVolume(event) {
        volume = event.target.value;
        audio.volume = isMuted ? 0 : volume;
    }

    function toggleMute() {
        isMuted = !isMuted;
        audio.volume = isMuted ? 0 : volume;
    }

    function seek(event) {
        const seekTime = (event.target.value / 100) * duration;
        audio.currentTime = seekTime;
        currentTime = seekTime;
    }

    function selectPlaybackRate(rate) {
        playbackRate = rate;
        audio.playbackRate = playbackRate;
        showRates = false;
    }

    function formatTime(totalSeconds) {
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = Math.floor(totalSeconds % 60);

        return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
    }

    function nextTrack() {
        if ($chapterIndexStore < $playlistStore.length - 1) {
            $chapterIndexStore++;
        }
    }

    function prevTrack() {
        if ($chapterIndexStore > 0) {
            $chapterIndexStore--;
        }
    }

    function closePlayer() {
        $isPlayingStore = false;
        $isOpenStore = false;
    }

    function toggleRateDropdown() {
        showRates = !showRates;
    }

    onMount(() => {
        audio.addEventListener('timeupdate', () => {
            currentTime = audio.currentTime;
        });
        audio.addEventListener('loadedmetadata', () => {
            duration = audio.duration;
        });
        audio.addEventListener('ended', () => {
            if ($chapterIndexStore < $playlistStore.length - 1) {
                nextTrack();
            } else {
                $isPlayingStore = false;
            }
        });

        document.addEventListener('keydown', handleSpacebar);
        document.addEventListener('click', (event) => {
            if (!event.target.closest('.rate-selector')) {
                showRates = false;
            }
        });
    });

    onDestroy(() => {
        document.removeEventListener('keydown', handleSpacebar);
    });
</script>

<div class="fixed bottom-0 w-full md:ml-64 md:w-[calc(100%-16rem)]" class:hidden={!$isOpenStore}>
    <div
        class="relative mx-auto flex w-full max-w-3xl flex-col items-center rounded-lg bg-emerald-950 px-6 py-2 text-white shadow-lg"
    >
        <button
            type="button"
            class="absolute right-2 top-2 text-xl text-white hover:text-red-500"
            on:click={closePlayer}
        >
            <XSvg />
        </button>
        <div class="mb-4 w-full text-center text-lg font-semibold">{$playlistStore[$chapterIndexStore]?.name}</div>
        <div class="mb-2 flex w-full items-center">
            <div class="rate-selector relative">
                <button
                    type="button"
                    class="rate-button absolute -top-2.5 text-sm text-gray-400 hover:text-white focus:outline-none"
                    on:click={toggleRateDropdown}
                >
                    {playbackRate}x
                </button>
                {#if showRates}
                    <div class="absolute bottom-0 z-10 mt-1 w-20 rounded-md bg-gray-800 shadow-md">
                        {#each playbackRates as rate (rate)}
                            <button
                                type="button"
                                class="block w-full px-2 py-1 text-left text-sm text-gray-300 hover:bg-gray-700"
                                on:click={() => selectPlaybackRate(rate)}
                            >
                                {rate}x
                            </button>
                        {/each}
                    </div>
                {/if}
            </div>
            <div class="mx-auto flex items-center space-x-4">
                <button
                    type="button"
                    class="text-2xl {$chapterIndexStore === 0
                        ? 'cursor-not-allowed text-gray-500'
                        : 'text-gray-400 hover:text-white'}"
                    disabled={$chapterIndexStore === 0}
                    on:click={prevTrack}
                >
                    <BackwardStepSvg svgClass="w-12 h-12" />
                </button>
                <button
                    type="button"
                    class="flex h-12 w-12 items-center justify-center text-2xl text-gray-400 hover:text-white"
                    on:click={togglePlay}
                >
                    {#if $isPlayingStore}
                        <PauseCircleSvg svgClass="w-12 h-12" />
                    {:else}
                        <PlayCircleSvg svgClass="w-12 h-12" />
                    {/if}
                </button>
                <button
                    type="button"
                    class="text-2xl {$chapterIndexStore === $playlistStore.length - 1
                        ? 'cursor-not-allowed text-gray-500'
                        : 'text-gray-400 hover:text-white'}"
                    on:click={nextTrack}
                >
                    <ForwardStepSvg svgClass="w-12 h-12" />
                </button>
            </div>
        </div>
        <div class="mb-2 flex w-full items-center">
            <span class="text-sm text-gray-400">{formatTime(currentTime)}</span>
            <div class="relative mx-2 h-1 flex-1 overflow-hidden rounded-lg bg-gray-500">
                <div
                    style:width="{(currentTime / duration) * 100}%"
                    class="absolute left-0 top-0 h-full bg-white"
                ></div>
                <input
                    type="range"
                    class="absolute left-0 top-0 h-full w-full cursor-pointer opacity-0"
                    min="0"
                    max="100"
                    value={(currentTime / duration) * 100}
                    on:input={seek}
                />
            </div>
            <span class="text-sm text-gray-400">{formatTime(duration)}</span>
        </div>
        <div class="flex w-5/6 items-center justify-center">
            <button type="button" class="text-xl text-gray-400 hover:text-white" on:click={toggleMute}>
                {#if isMuted}
                    <VolumeMutedSvg />
                {:else}
                    <VolumeSvg />
                {/if}
            </button>
            <input
                type="range"
                class="mx-2 h-1 w-24 appearance-none rounded-lg bg-gray-500"
                min="0"
                max="1"
                step="0.01"
                bind:value={volume}
                on:input={changeVolume}
            />
        </div>
    </div>
</div>
