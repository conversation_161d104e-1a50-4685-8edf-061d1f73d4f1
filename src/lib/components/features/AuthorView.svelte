<script lang="ts">
    import AuthorsApiClient from '$lib/api/AuthorsApiClient';
    import Container from '$lib/components/layout/body/Container.svelte';
    import UserMinusSvg from '$lib/components/svg/UserMinusSvg.svelte';
    import UserPlusSvg from '$lib/components/svg/UserPlusSvg.svelte';
    import BookListUI from '$lib/components/ui/BookListUI.svelte';
    import IconButton from '$lib/components/ui/IconButton.svelte';
    import type Author from '$lib/domain/Author';
    import {t} from '$lib/localization/Localization';
    import BookSearchFiltersService from '$lib/services/BookSearchFiltersService';
    import StringService from '$lib/services/StringService';

    export let author: Author;

    async function follow() {
        const authorsApiClient = new AuthorsApiClient();
        const response = await authorsApiClient.follow(author.id);

        if (response.ok && response.data.success) {
            author.isFollowed = true;
        }
    }

    async function unfollow() {
        const authorsApiClient = new AuthorsApiClient();
        const response = await authorsApiClient.unfollow(author.id);

        if (response.ok && response.data.success) {
            author.isFollowed = false;
        }
    }
</script>

<Container extraClass="space-x-8 lg:flex">
    {#if author.photo}
        <div class="mx-auto mb-4 w-1/2 sm:w-1/3 lg:w-1/5 lg:flex-col">
            <img src={author.photo} class="w-full" alt="Author" />
        </div>
    {/if}
    <div class="lg:w-4/5 lg:flex-col">
        <h1 class="text-3xl font-bold">{author.name}</h1>
        {#if author.birth_date}
            <div>
                {author.birth_date}
                {#if author.death_date}
                    - {author.death_date}
                {/if}
            </div>
        {/if}
        <div class="mt-6 space-y-6">
            <div>
                {#if author.isFollowed}
                    <IconButton icon={UserMinusSvg} title={$t('author.unfollow')} callback={unfollow} />
                {:else}
                    <IconButton icon={UserPlusSvg} title={$t('author.follow')} callback={follow} />
                {/if}
            </div>
            {#if author.bio}
                <div>
                    {StringService.stripTags(author.bio)}
                </div>
            {/if}
            {#if author.subjects.length > 0}
                <div>
                    <span class="font-semibold">{$t('book.subjects')}:</span>
                    {#each author.subjects as subject, index (index)}
                        <button type="button" on:click={() => BookSearchFiltersService.goToSubjectSearch(subject)}>
                            {subject}
                        </button>
                        {#if index < author.subjects.length - 1}
                            <span>, </span>
                        {/if}
                    {/each}
                </div>
            {/if}
        </div>
    </div>
</Container>
<Container>
    <h1 class="text-2xl font-bold">{$t('author.books')}</h1>
    <div class="mt-5 space-y-4">
        <BookListUI books={author.books} />
    </div>
</Container>
