<script lang="ts">
    import {goto} from '$app/navigation';
    import Container from '$lib/components/layout/body/Container.svelte';
    import BookOpenSvg from '$lib/components/svg/BookOpenSvg.svelte';
    import PlaySvg from '$lib/components/svg/PlaySvg.svelte';
    import IconButton from '$lib/components/ui/IconButton.svelte';
    import type Audiobook from '$lib/domain/Audiobook';
    import {t} from '$lib/localization/Localization';
    import AudiobookSearchFiltersService from '$lib/services/AudiobookSearchFiltersService';
    import AudioPlayerStores from '$lib/stores/AudioPlayerStores';
    import AppRoutes from '$routes/AppRoutes';

    export let audiobook: Audiobook;

    const isOpenStore = AudioPlayerStores.isOpen;
    const isPlayingStore = AudioPlayerStores.isPlaying;
    const chapterIndexStore = AudioPlayerStores.chapterIndex;
    const playlistStore = AudioPlayerStores.playlist;

    function playAudiobook(chapterIndex: number) {
        $playlistStore = audiobook.chapters;
        $chapterIndexStore = chapterIndex;
        $isOpenStore = true;
        $isPlayingStore = true;
    }
</script>

<Container extraClass="space-x-8 lg:flex">
    <div class="lg:w-4/5 lg:flex-col">
        <h1 class="text-3xl font-bold">{audiobook.title}</h1>
        <div class="mb-6">
            <span>{$t('book.by')}</span>
            <button type="button" on:click={() => AudiobookSearchFiltersService.goToAuthorSearch(audiobook.creator)}>
                {audiobook.creator}
            </button>
        </div>
        <div class="mb-6 space-y-1">
            {#if audiobook.publicationDate}
                <div>
                    <span class="font-semibold">{$t('book.publishDate')}:</span>
                    <span>{audiobook.publicationDate}</span>
                </div>
            {/if}
            {#if audiobook.subjects.length > 0}
                <div>
                    <span class="font-semibold">{$t('book.subjects')}:</span>
                    {#each audiobook.subjects as subject, index (index)}
                        <button type="button" on:click={() => AudiobookSearchFiltersService.goToSubjectSearch(subject)}>
                            {subject}
                        </button>
                        {#if index < audiobook.subjects.length - 1}
                            <span>, </span>
                        {/if}
                    {/each}
                </div>
            {/if}
            {#if audiobook.language}
                <div>
                    <span class="font-semibold">{$t('book.languages')}:</span>
                    {audiobook.language.text}
                </div>
            {/if}
        </div>
        <div class="space-y-1">
            {#if audiobook.bookId}
                <div class="mb-6">
                    <IconButton
                        icon={BookOpenSvg}
                        title={$t('book.goToBook')}
                        callback={async () => {
                            await goto(AppRoutes.book(audiobook.bookId));
                        }}
                    />
                </div>
            {/if}
            {#if audiobook.chapters.length > 0}
                <div class="space-y-2">
                    {#each audiobook.chapters as chapter, index (index)}
                        <div>
                            <IconButton icon={PlaySvg} title={chapter.name} callback={() => playAudiobook(index)} />
                        </div>
                    {/each}
                </div>
            {/if}
        </div>
    </div>
</Container>
