<script lang="ts">
    import AuthenticationApiClient from '$lib/api/AuthenticationApiClient';
    import SocialLogin from '$lib/components/features/Authentication/SocialLogin.svelte';
    import AuthenticationContainer from '$lib/components/layout/body/AuthenticationContainer.svelte';
    import Button from '$lib/components/ui/Button.svelte';
    import TextInput from '$lib/components/ui/TextInput.svelte';
    import {t} from '$lib/localization/Localization';
    import AppRoutes from '$routes/AppRoutes';

    let form: HTMLFormElement;
    let name: string = '';
    let email: string = '';
    let password: string = '';
    let nameError: string | undefined;
    let emailError: string | undefined;
    let passwordError: string | undefined;
    let isVerificationEmailSent = false;

    async function handleRegister() {
        if (!form.checkValidity()) {
            return;
        }

        const authenticationApiClient = new AuthenticationApiClient();
        const response = await authenticationApiClient.register(name, email, password);

        if (!response.ok || !response.data.success) {
            if (response.data.errors.name) {
                nameError = response.data.errors.name[0];
            }

            if (response.data.errors.email) {
                emailError = response.data.errors.email[0];
            }

            if (response.data.errors.password) {
                passwordError = response.data.errors.password[0];
            }

            return;
        }

        isVerificationEmailSent = true;
    }
</script>

<AuthenticationContainer>
    <h1 class="mb-6 text-center text-2xl font-semibold">{$t('auth.signup')}</h1>
    {#if !isVerificationEmailSent}
        <form
            bind:this={form}
            class="flex flex-col items-center justify-between space-y-3 md:flex-row"
            on:submit|preventDefault={handleRegister}
        >
            <div class="w-full space-y-4">
                <TextInput title={$t('fields.name')} bind:value={name} showLabel required error={nameError} />
                <TextInput title={$t('fields.email')} bind:value={email} email showLabel required error={emailError} />
                <TextInput
                    title={$t('fields.password')}
                    bind:value={password}
                    password
                    showLabel
                    required
                    error={passwordError}
                />
                <div class="text-center">
                    <Button extraClass="block w-1/2 mt-4" title={$t('auth.signup')} />
                </div>
                <div class="text-center text-gray-500 dark:text-gray-400">{$t('auth.or')}</div>
                <SocialLogin />
                <div class="text-center">
                    <span class="text-gray-500 dark:text-gray-400">{$t('auth.haveAccount')}</span>
                    <a href={AppRoutes.login} class="underline">{$t('auth.login')}</a>
                </div>
            </div>
        </form>
    {:else}
        <div>{$t('auth.verificationEmailSent')}</div>
    {/if}
</AuthenticationContainer>
