<script lang="ts">
    import {goto} from '$app/navigation';
    import AuthenticationApiClient from '$lib/api/AuthenticationApiClient';
    import SocialLogin from '$lib/components/features/Authentication/SocialLogin.svelte';
    import AuthenticationContainer from '$lib/components/layout/body/AuthenticationContainer.svelte';
    import Button from '$lib/components/ui/Button.svelte';
    import TextInput from '$lib/components/ui/TextInput.svelte';
    import {t} from '$lib/localization/Localization';
    import AuthenticationService from '$lib/services/AuthenticationService';
    import AppRoutes from '$routes/AppRoutes';

    let form: HTMLFormElement;
    let email: string = '';
    let password: string = '';
    let error: string;

    async function handleLogin() {
        if (!form.checkValidity()) {
            return;
        }

        const authenticationApiClient = new AuthenticationApiClient();
        const response = await authenticationApiClient.login(email, password);

        if (!response.ok) {
            error = response.data.errors.email[0];

            return;
        }

        AuthenticationService.login(response.data.token, response.data.user);
        await goto(AppRoutes.home);
    }
</script>

<AuthenticationContainer>
    <h1 class="mb-6 text-center text-2xl font-semibold">{$t('auth.login')}</h1>
    <form
        bind:this={form}
        class="flex flex-col items-center justify-between space-y-3 md:flex-row"
        on:submit|preventDefault={handleLogin}
    >
        <div class="w-full space-y-4">
            <TextInput title={$t('fields.email')} bind:value={email} email showLabel required error={error} />
            <a href={AppRoutes.resetPassword} class="relative z-10 float-right underline" tabindex="-1">
                {$t('auth.forgotPassword')}
            </a>
            <TextInput title={$t('fields.password')} bind:value={password} password showLabel required />
            <div class="text-center">
                <Button extraClass="block w-1/2 mt-4" title={$t('auth.login')} />
            </div>
            <div class="text-center text-gray-500 dark:text-gray-400">{$t('auth.or')}</div>
            <SocialLogin />
            <div class="text-center">
                <span class="text-gray-500 dark:text-gray-400">{$t('auth.noAccount')}</span>
                <a href={AppRoutes.register} class="underline">{$t('auth.signup')}</a>
            </div>
        </div>
    </form>
</AuthenticationContainer>
