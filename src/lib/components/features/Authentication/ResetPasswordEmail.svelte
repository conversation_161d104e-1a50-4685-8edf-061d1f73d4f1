<script lang="ts">
    import AuthenticationApiClient from '$lib/api/AuthenticationApiClient';
    import AuthenticationContainer from '$lib/components/layout/body/AuthenticationContainer.svelte';
    import Button from '$lib/components/ui/Button.svelte';
    import TextInput from '$lib/components/ui/TextInput.svelte';
    import {t} from '$lib/localization/Localization';

    let form: HTMLFormElement;
    let email: string;
    let error: string;
    let isEmailSent = false;

    async function handleResetPasswordEmail() {
        if (!form.checkValidity()) {
            return;
        }

        const authenticationApiClient = new AuthenticationApiClient();
        const response = await authenticationApiClient.sendResetPasswordEmail(email);

        if (!response.ok || !response.data.success) {
            error = t.get('notifications.genericError', {}, {});

            return;
        }

        isEmailSent = true;
    }
</script>

<AuthenticationContainer>
    <h1 class="mb-6 text-center text-2xl font-semibold">{$t('auth.resetPassword')}</h1>
    {#if !isEmailSent}
        <div class="pb-4">{$t('auth.resetPasswordEmail')}</div>
        <form
            bind:this={form}
            class="flex flex-col items-center justify-between space-y-3 md:flex-row"
            on:submit|preventDefault={handleResetPasswordEmail}
        >
            <div class="w-full space-y-4">
                <TextInput title={$t('fields.email')} bind:value={email} email showLabel required error={error} />
                <div class="text-center">
                    <Button extraClass="block w-1/2 mt-4" title={$t('auth.resetPassword')} />
                </div>
            </div>
        </form>
    {:else}
        <div>{$t('auth.resetPasswordEmailSent')}</div>
    {/if}
</AuthenticationContainer>
