<script lang="ts">
    import {onMount} from 'svelte';
    import {goto} from '$app/navigation';
    import {PUBLIC_APP_URL, PUBLIC_APPLE_OAUTH_CLIENT_ID, PUBLIC_GOOGLE_OAUTH_CLIENT_ID} from '$env/static/public';
    import AuthenticationApiClient from '$lib/api/AuthenticationApiClient';
    import AuthenticationService from '$lib/services/AuthenticationService';
    import DOMService from '$lib/services/DOMService.ts';
    import AppRoutes from '$routes/AppRoutes';

    function setupGoogleLogin() {
        DOMService.loadScript('https://accounts.google.com/gsi/client');
        window.onGoogleLoggedIn = onGoogleLoggedIn;
    }

    function setupAppleLogin() {
        //https://developer.apple.com/documentation/sign_in_with_apple/sign_in_with_apple_js/incorporating_sign_in_with_apple_into_other_platforms see locale
        const locale = 'en_US';
        DOMService.loadScript(
            `https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/${locale}/appleid.auth.js`,
            () => {
                window.AppleID.auth.init({
                    clientId: PUBLIC_APPLE_OAUTH_CLIENT_ID,
                    scope: 'name email',
                    redirectURI: PUBLIC_APP_URL,
                    usePopup: true,
                });
            },
        );
        document.addEventListener('AppleIDSignInOnSuccess', onAppleSignIn);
    }

    async function onGoogleLoggedIn(credentialToken) {
        window.onGoogleLoggedIn = undefined;

        const authenticationApiClient = new AuthenticationApiClient();
        const response = await authenticationApiClient.googleLogin(credentialToken.credential);

        if (response.ok) {
            AuthenticationService.login(response.data.token, response.data.user);
            await goto(AppRoutes.home);
        }
    }

    async function onAppleSignIn(successEvent) {
        document.removeEventListener('AppleIDSignInOnSuccess', onAppleSignIn);

        const authenticationApiClient = new AuthenticationApiClient();
        const response = await authenticationApiClient.appleLogin(
            successEvent.detail.authorization.id_token,
            successEvent.detail.user,
        );

        if (response.ok) {
            AuthenticationService.login(response.data.token, response.data.user);
            await goto(AppRoutes.home);
        }
    }

    onMount(() => {
        setupGoogleLogin();
        setupAppleLogin();
    });
</script>

<div class="flex flex-col items-center justify-evenly space-y-1 sm:flex-row sm:space-y-0">
    <div
        id="appleid-signin"
        data-mode="center-align"
        data-type="sign-in"
        data-color="white"
        data-border="false"
        data-border-radius="12"
        data-width="200"
        data-height="40"
    ></div>
    <div
        id="g_id_onload"
        data-client_id={PUBLIC_GOOGLE_OAUTH_CLIENT_ID}
        data-context="signup"
        data-ux_mode="popup"
        data-callback="onGoogleLoggedIn"
    ></div>
    <div
        class="g_id_signin"
        data-type="standard"
        data-shape="rectangular"
        data-theme="outline"
        data-text="signin_with"
        data-size="large"
        data-logo_alignment="left"
        data-width="200"
    ></div>
</div>
