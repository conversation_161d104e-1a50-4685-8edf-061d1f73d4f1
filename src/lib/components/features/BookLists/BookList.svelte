<script lang="ts">
    import {goto} from '$app/navigation';
    import BookListsApiClient from '$lib/api/BookListsApiClient';
    import BooksApiClient from '$lib/api/BooksApiClient.js';
    import Container from '$lib/components/layout/body/Container.svelte';
    import BookList<PERSON> from '$lib/components/ui/BookListUI.svelte';
    import Button from '$lib/components/ui/Button.svelte';
    import Modal from '$lib/components/ui/Modal.svelte';
    import TextInput from '$lib/components/ui/TextInput.svelte';
    import type BookList from '$lib/domain/BookList';
    import {t} from '$lib/localization/Localization';
    import ToastNotificationsStore from '$lib/stores/ToastNotificationsStore';
    import AppRoutes from '$routes/AppRoutes';

    export let bookList: BookList;

    let openUpdateListModal = false;
    let listTitle = bookList.name;

    async function updateList() {
        const bookListsApiClient = new BookListsApiClient();
        const response = await bookListsApiClient.update(bookList.id, listTitle);

        if (!response.ok) {
            ToastNotificationsStore.push({
                text: t.get('notifications.genericError', {}, {}),
                type: 'warning',
            });

            return;
        }

        bookList.name = listTitle;
        openUpdateListModal = false;
    }

    async function deleteList() {
        const bookListsApiClient = new BookListsApiClient();
        const response = await bookListsApiClient.destroy(bookList.id);

        if (!response.ok) {
            ToastNotificationsStore.push({
                text: t.get('notifications.genericError', {}, {}),
                type: 'warning',
            });

            return;
        }

        await goto(AppRoutes.myLibrary);
    }

    async function removeBookFromList(bookId: string) {
        const booksApiClient = new BooksApiClient();
        const response = await booksApiClient.removeFromList(bookId, bookList.id);

        if (!response.ok) {
            ToastNotificationsStore.push({
                text: t.get('notifications.genericError', {}, {}),
                type: 'warning',
            });
        }

        bookList.books = bookList.books.filter((book) => book.uuid !== bookId);
    }
</script>

<Container title={bookList.name}>
    <div slot="actions" class="space-x-3">
        {#if bookList.id !== ''}
            <Button
                callback={() => {
                    openUpdateListModal = true;
                }}
                title={$t('viewList.update')}
            />
            <Modal bind:open={openUpdateListModal}>
                <div class="mb-4 flex justify-between rounded-t sm:mb-5">
                    <div class="text-lg text-gray-900 dark:text-white md:text-xl">
                        <h3 class="font-semibold">{$t('addList.addList')}</h3>
                    </div>
                </div>
                <div class="space-y-4">
                    <form class="space-y-3" on:submit|preventDefault={updateList}>
                        <TextInput title={$t('addList.listName')} bind:value={listTitle} />
                        <Button title={$t('addList.submit')} />
                    </form>
                </div>
            </Modal>
            <Button
                extraClass="bg-red-600 dark:bg-red-600 text-white dark:text-white hover:bg-red-800 dark:hover:bg-red-800"
                callback={deleteList}
                title={$t('viewList.delete')}
            />
        {/if}
    </div>
    {#if bookList.id === ''}
        <BookListUI books={bookList.books} />
    {:else}
        <BookListUI books={bookList.books} deleteCallback={removeBookFromList} />
    {/if}
</Container>
