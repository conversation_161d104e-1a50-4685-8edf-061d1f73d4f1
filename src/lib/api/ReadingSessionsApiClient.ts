import type {BaseApiResponse} from '$lib/api/base/BaseApiClient';
import MainApiClient from '$lib/api/base/MainApiClient';
import type ReadingSession from '$lib/domain/ReadingSession';

export default class ReadingSessionsApiClient extends MainApiClient {
    public async index(bookId: string): Promise<IndexResponse> {
        return await this.get(`/reading-sessions/book/${bookId}`);
    }

    public async store(sessionData: CreateReadingSessionRequest): Promise<StoreResponse> {
        return await this.post('/reading-sessions', sessionData);
    }

    public async update(sessionId: string, sessionData: UpdateReadingSessionRequest): Promise<UpdateResponse> {
        return await this.put(`/reading-sessions/${sessionId}`, sessionData);
    }

    public async destroy(sessionId: string): Promise<DestroyResponse> {
        return await this.delete(`/reading-sessions/${sessionId}`);
    }
}

interface IndexResponse extends BaseApiResponse {
    data: ReadingSession[];
}

interface StoreResponse extends BaseApiResponse {
    data: {
        session: ReadingSession;
        success: boolean;
    };
}

interface UpdateResponse extends BaseApiResponse {
    data: {
        session: ReadingSession;
        success: boolean;
    };
}

interface DestroyResponse extends BaseApiResponse {
    data: {
        success: boolean;
    };
}

interface CreateReadingSessionRequest {
    bookId: string;
    languageCode?: string;
    startDate?: string;
    endDate?: string;
    pagesRead?: number;
    totalPages?: number;
}

interface UpdateReadingSessionRequest {
    languageCode?: string;
    startDate?: string;
    endDate?: string;
    pagesRead?: number;
    totalPages?: number;
}
