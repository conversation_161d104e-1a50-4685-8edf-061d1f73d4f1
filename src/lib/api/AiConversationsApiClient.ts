import type {BaseApiResponse} from '$lib/api/base/BaseApiClient';
import MainApiClient from '$lib/api/base/MainApiClient';
import type AiConversation from '$lib/domain/AiConversation';

export default class AiConversationsApiClient extends MainApiClient {
    public async index(): Promise<IndexResponse> {
        return await this.get('/ai-conversations');
    }

    public async sendMessage(message: string, conversationId: number | null): Promise<SendMessageResponse> {
        return await this.post('/ai-conversations', {
            content: message,
            conversationId: conversationId,
        });
    }

    public async deleteConversation(id: number): Promise<DeleteConversationResponse> {
        return await this.delete(`/ai-conversations/${id}`);
    }
}

interface IndexResponse extends BaseApiResponse {
    data: AiConversation[];
}

interface SendMessageResponse extends BaseApiResponse {
    data: {
        conversationId: number;
        reply: string;
        limitReached: boolean;
        messageLimitReached: boolean;
    };
}

interface DeleteConversationResponse extends BaseApiResponse {
    data: {
        success: boolean;
    };
}
