import type {BaseApiResponse} from '$lib/api/base/BaseApiClient';
import MainApiClient from '$lib/api/base/MainApiClient';
import type Book from '$lib/domain/Book';

export default class LikesApiClient extends MainApiClient {
    public async index(full: boolean = true): Promise<IndexResponse> {
        return await this.get('/likes', {full: full ? '1' : '0'});
    }

    public async like(bookId: string): Promise<LikeResponse> {
        return await this.post(`/likes/like/${bookId}`);
    }

    public async dislike(bookId: string): Promise<DislikeResponse> {
        return await this.post(`/likes/dislike/${bookId}`);
    }

    public async removeLike(bookId: string): Promise<RemoveLikeResponse> {
        return await this.post(`/likes/remove/${bookId}`);
    }
}

interface IndexResponse extends BaseApiResponse {
    data: Book[];
}

interface LikeResponse extends BaseApiResponse {
    data: {
        success: boolean;
    };
}

interface DislikeResponse extends BaseApiResponse {
    data: {
        success: boolean;
    };
}

interface RemoveLikeResponse extends BaseApiResponse {
    data: {
        success: boolean;
    };
}
