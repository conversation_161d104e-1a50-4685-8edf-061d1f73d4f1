import type {BaseApiResponse} from '$lib/api/base/BaseApiClient';
import MainApiClient from '$lib/api/base/MainApiClient';
import type Recommendations from '$lib/domain/Recommendations';

export default class RecommendationsApiClient extends MainApiClient {
    public async index(): Promise<IndexResponse> {
        return await this.get('/recommendations');
    }
}

interface IndexResponse extends BaseApiResponse {
    data: Recommendations;
}
