import type {BaseApiResponse} from '$lib/api/base/BaseApiClient';
import MainApiClient from '$lib/api/base/MainApiClient';
import type Book from '$lib/domain/Book';
import type BookSummary from '$lib/domain/BookSummary';

export default class BooksApiClient extends MainApiClient {
    public async index(onlyFree: boolean = false): Promise<IndexResponse> {
        return await this.get('/books', {onlyFree: onlyFree ? '1' : '0'});
    }

    public async show(id: string): Promise<ShowResponse> {
        return await this.get(`/books/${id}`);
    }

    public async similar(id: string): Promise<SimilarResponse> {
        return await this.get(`/books/${id}/similar`);
    }

    public async setNotFree(id: string): Promise<SetNotFreeResponse> {
        return await this.post(`/books/${id}/not-free`);
    }

    public async addToList(bookId: string, bookListId: string): Promise<AddToListResponse> {
        return await this.post(`/books/${bookId}/list/${bookListId}/add`);
    }

    public async removeFromList(bookId: string, bookListId: string): Promise<RemoveFromListResponse> {
        return await this.post(`/books/${bookId}/list/${bookListId}/remove`);
    }

    public async share(bookId: string, network: string): Promise<ShareResponse> {
        return await this.post(`/books/${bookId}/share`, {network});
    }

    public async summary(id: string): Promise<SummaryResponse> {
        return await this.post(`/books/${id}/summary`);
    }
}

interface IndexResponse extends BaseApiResponse {
    data: Book[];
}

interface ShowResponse extends BaseApiResponse {
    data: Book;
}

interface SimilarResponse extends BaseApiResponse {
    data: Book[];
}

interface SetNotFreeResponse extends BaseApiResponse {
    data: {
        success: boolean;
    };
}

interface AddToListResponse extends BaseApiResponse {
    data: {
        success: boolean;
    };
}

interface RemoveFromListResponse extends BaseApiResponse {
    data: {
        success: boolean;
    };
}

interface ShareResponse extends BaseApiResponse {
    data: {
        success: boolean;
    };
}

interface SummaryResponse extends BaseApiResponse {
    data: BookSummary;
}
