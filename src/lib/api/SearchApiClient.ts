import {PUBLIC_SEARCH_API_KEY_PROD, PUBLIC_SEARCH_API_URL} from '$env/static/public';
import BaseApiClient, {type BaseApiResponse} from '$lib/api/base/BaseApiClient';
import type SearchBook from '$lib/domain/SearchBook';

export default class SearchApiClient extends BaseApiClient {
    protected readonly apiUrl: string = PUBLIC_SEARCH_API_URL;
    protected readonly apiKey: string | null = PUBLIC_SEARCH_API_KEY_PROD;

    public async books(request: BooksRequest): Promise<BooksResponse> {
        type searchParametersType = {
            q: string;
            filter: Array<string | number | Array<string | number>>;
            offset: number;
            limit: number;
        };

        const defaultOffset = 0;
        const defaultLimit = 20;
        const searchParameters: searchParametersType = {
            // these are also Meilisearch's defaults
            q: request.title ?? '',
            filter: [],
            offset: request.offset ?? defaultOffset,
            limit: request.limit ?? defaultLimit,
        };

        if (request.author !== undefined) {
            searchParameters.filter.push(`authors = "${request.author}"`);
        }
        if (request.isbn !== undefined) {
            searchParameters.filter.push(
                `identifiers.ISBN13 = "${request.isbn}" OR identifiers.isbn13 = "${request.isbn}"
                OR identifiers.ISBN10 = "${request.isbn}" OR identifiers.isbn10 = "${request.isbn}"
                OR identifiers.ISBN = "${request.isbn}" OR identifiers.isbn = "${request.isbn}"`,
            );
        }
        if (request.language !== undefined) {
            searchParameters.filter.push(`languages.value = "${request.language}"`);
        }
        if (request.onlyFree) {
            searchParameters.filter.push('isFree = true');
        }
        if (request.subject !== undefined) {
            searchParameters.filter.push(`subjects = "${request.subject}"`);
        }
        if (request.minPublishYear !== undefined) {
            searchParameters.filter.push(`publishDates >= ${request.minPublishYear}`);
        }
        if (request.maxPublishYear !== undefined) {
            searchParameters.filter.push(`publishDates <= ${request.maxPublishYear}`);
        }

        return await this.post('/indexes/books/search', searchParameters);
    }
}

interface BooksRequest {
    title?: string;
    author?: string;
    subject?: string;
    isbn?: string;
    onlyFree?: boolean;
    minPublishYear?: number;
    maxPublishYear?: number;
    language?: string;
    offset?: number;
    limit?: number;
}

interface SearchResponse extends BaseApiResponse {
    data: {
        hits: object[];
        estimatedTotalHits: number;
    };
}

interface BooksResponse extends SearchResponse {
    data: {
        hits: SearchBook[];
        estimatedTotalHits: number;
    };
}
