import type {BaseApiResponse} from '$lib/api/base/BaseApiClient';
import MainApiClient from '$lib/api/base/MainApiClient';
import type BookList from '$lib/domain/BookList';
import type BookLists from '$lib/domain/BookLists';

export default class BookListsApiClient extends MainApiClient {
    public async index(): Promise<IndexResponse> {
        return await this.get('/book-lists');
    }

    public async store(name: string): Promise<StoreResponse> {
        return await this.post('/book-lists', {name});
    }

    public async show(id: string): Promise<ShowResponse> {
        return await this.get(`/book-lists/${id}`);
    }

    public async update(id: string, name: string): Promise<UpdateResponse> {
        return await this.post(`/book-lists/${id}`, {name});
    }

    public async destroy(id: string): Promise<DestroyResponse> {
        return await this.delete(`/book-lists/${id}`);
    }
}

interface IndexResponse extends BaseApiResponse {
    data: BookLists;
}

interface StoreResponse extends BaseApiResponse {
    data: {
        success: boolean;
    };
}

interface ShowResponse extends BaseApiResponse {
    data: BookList;
}

interface UpdateResponse extends BaseApiResponse {
    data: {
        success: boolean;
    };
}

interface DestroyResponse extends BaseApiResponse {
    data: {
        success: boolean;
    };
}
