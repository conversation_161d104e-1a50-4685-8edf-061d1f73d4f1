import I18n, {type Config} from 'sveltekit-i18n';

// This constant and the LocaleType could be automatically generated from the translations folder so that the new
// languages are automatically loaded
export const languages = [
    {
        locale: 'en',
        name: 'English',
    },
    {
        locale: 'es',
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    },
    {
        locale: 'fr',
        name: 'Fran<PERSON>',
    },
];

const config: Config = {
    loaders: [],
    preprocess: 'full',
    initLocale: 'en',
    fallbackLocale: 'en', // if this option is not set the build version of the app gives warnings
};

languages.forEach((language) => {
    config.loaders?.push({
        locale: language.locale,
        routes: undefined,
        key: '',
        loader: async () => (await import(`./translations/${language.locale}.json`)).default,
    });
});

export const {t, locale, locales, loading, loadTranslations} = new I18n(config);
export type LocaleType = 'en' | 'es' | 'fr';
