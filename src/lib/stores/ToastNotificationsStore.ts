import {writable} from 'svelte/store';
import StringService from '$lib/services/StringService';

export type toastNotificationTypeType = 'warning' | 'success';
type toastNotificationType = {
    text: string;
    type: toastNotificationTypeType;
};
type deletableToastNotificationType = toastNotificationType & {
    id: string;
};

const store = writable<deletableToastNotificationType[]>([]);

export default {
    subscribe: store.subscribe,
    push: (newToastNotification: toastNotificationType) => {
        store.update((toastNotifications) => {
            toastNotifications.push({
                id: StringService.generatePseudoUniqueString('toast'),
                text: newToastNotification.text,
                type: newToastNotification.type,
            });

            return toastNotifications;
        });
    },
    remove: (id: string) => {
        store.update((toastNotifications) => {
            const index = toastNotifications.findIndex((item) => item.id === id);
            if (index < 0) {
                throw new Error('Invalid toast notification index.');
            }

            toastNotifications.splice(index, 1);

            return toastNotifications;
        });
    },
};
