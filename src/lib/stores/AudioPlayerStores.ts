import {type Writable, writable} from 'svelte/store';
import type AudiobookChapter from '$lib/domain/AudiobookChapter';

type AudioPlayerStores = {
    isOpen: Writable<boolean>;
    isPlaying: Writable<boolean>;
    chapterIndex: Writable<number | null>;
    playlist: Writable<AudiobookChapter[]>;
};

const stores: AudioPlayerStores = {
    isOpen: writable(false),
    isPlaying: writable(false),
    chapterIndex: writable(null),
    playlist: writable([]),
};

export default stores;
