import {goto} from '$app/navigation';
import AudiobookSearchFiltersStores, {resetAudiobookSearchFilters} from '$lib/stores/AudiobookSearchFiltersStores';
import AppRoutes from '$routes/AppRoutes';

export default {
    goToAuthorSearch: async function (author: string): Promise<void> {
        resetAudiobookSearchFilters();
        AudiobookSearchFiltersStores.author.set({
            label: author,
            value: author,
            index: 0,
        });

        await goto(AppRoutes.audiobooks);
    },
    goToSubjectSearch: async function (subject: string): Promise<void> {
        resetAudiobookSearchFilters();
        AudiobookSearchFiltersStores.subject.set({
            label: subject,
            value: subject,
            index: 0,
        });

        await goto(AppRoutes.audiobooks);
    },
};
