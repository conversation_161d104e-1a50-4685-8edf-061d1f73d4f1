import type Language from '$lib/domain/Language';

type SearchBook = {
    authors: string[];
    cover: string;
    id: string;
    identifiers: Record<string, string>;
    isFree: number;
    languages: Language[];
    popularity: number;
    publishDates: number[];
    subjects: string[];
    subtitle: string;
    title: string;
    urlIdentifier: {key: string; type: string};
    uuid: string;
};

export default SearchBook;
