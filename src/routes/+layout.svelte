<script lang="ts">
    import '$lib/css/App.css';
    import {initFlowbite} from 'flowbite';
    import {onMount} from 'svelte';
    import {get} from 'svelte/store';
    import {browser} from '$app/environment';
    import SettingsApiClient from '$lib/api/SettingsApiClient';
    import AudioPlayer from '$lib/components/features/AudioPlayer/AudioPlayer.svelte';
    import Footer from '$lib/components/layout/footer/Footer.svelte';
    import Header from '$lib/components/layout/header/Header.svelte';
    import Sidebar from '$lib/components/layout/sidebar/Sidebar.svelte';
    import ToastNotifications from '$lib/components/layout/ToastNotifications.svelte';
    import AudioPlayerStores from '$lib/stores/AudioPlayerStores';
    import AuthenticatedStore from '$lib/stores/AuthenticatedStore';
    import LocaleStore from '$lib/stores/LocaleStore';

    let isOpenStore = AudioPlayerStores.isOpen;
    let initialLocale = $LocaleStore;
    $: if (get(AuthenticatedStore) && $LocaleStore !== initialLocale) {
        // client needs to be instantiated each time in case the user logs in and the api token changes
        const settingsApiClient = new SettingsApiClient();

        // TypeScript wrongly reports these types as different
        settingsApiClient.updateLanguage($LocaleStore).then();
        initialLocale = $LocaleStore;
    }

    onMount(() => {
        initFlowbite();
    });
</script>

<svelte:head>
    <title>Liberom</title>
</svelte:head>

<div class="bg-white text-gray-900 subpixel-antialiased dark:bg-gray-900 dark:text-white">
    <Header />
    <Sidebar />
    <main class="h-auto min-h-screen p-8 pt-16 md:ml-64">
        <slot />
    </main>
    <Footer />
    <ToastNotifications />
    {#if browser && $isOpenStore}
        <AudioPlayer />
    {/if}
</div>
