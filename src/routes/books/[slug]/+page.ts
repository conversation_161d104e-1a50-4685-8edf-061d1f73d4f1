import {error} from '@sveltejs/kit';
import BooksApiClient from '$lib/api/BooksApiClient';
import StatusCodes from '$routes/StatusCodes';
import type {PageLoad} from './$types';

/*
 * Pre-rendering should be disabled for the book pages because there are too many of them. In the future we could enable
 * this just for the most popular ones. SSR is also disabled because we only use it for pre-rendering.
 */
export const prerender = false;
export const ssr = false;

export const load: PageLoad = async function ({fetch, params}) {
    const booksApiClient = new BooksApiClient(fetch);
    const response = await booksApiClient.show(params.slug);

    if (!response.ok) {
        throw error(StatusCodes.clientError.notFound, 'Not found');
    }

    return response.data;
};
