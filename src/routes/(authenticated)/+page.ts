import {error} from '@sveltejs/kit';
import RecommendationsApiClient from '$lib/api/RecommendationsApiClient';
import StatusCodes from '$routes/StatusCodes';
import type {PageLoad} from './$types';

export const load: PageLoad = async function ({fetch}) {
    const recommendationsApiClient = new RecommendationsApiClient(fetch);
    const response = await recommendationsApiClient.index();

    if (!response.ok) {
        throw error(StatusCodes.clientError.notFound, 'Not found');
    }

    return response.data;
};
