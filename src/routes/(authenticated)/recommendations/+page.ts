import {error} from '@sveltejs/kit';
import StatusCodes from '$routes/StatusCodes';
import type {PageLoad} from './$types';
import AiConversationsApiClient from '$lib/api/AiConversationsApiClient';

export const load: PageLoad = async function ({fetch}) {
    const aiConversationsApiClient = new AiConversationsApiClient(fetch);
    const response = await aiConversationsApiClient.index();

    if (!response.ok) {
        throw error(StatusCodes.clientError.notFound, 'Not found');
    }

    return {
        conversations: response.data,
    };
};
