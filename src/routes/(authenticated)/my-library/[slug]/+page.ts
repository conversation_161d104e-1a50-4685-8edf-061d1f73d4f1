import {error} from '@sveltejs/kit';
import BookListsApiClient from '$lib/api/BookListsApiClient';
import BookReadStatusesApiClient from '$lib/api/BookReadStatusesApiClient';
import LikesApiClient from '$lib/api/LikesApiClient';
import type BookList from '$lib/domain/BookList';
import {isBookReadStatusType} from '$lib/domain/BookReadStatusType';
import {t} from '$lib/localization/Localization';
import StatusCodes from '$routes/StatusCodes';
import type {PageLoad} from './$types';

export const load: PageLoad = async function ({fetch, params, url}) {
    if (params.slug === 'likes') {
        return getLikesList(fetch);
    }

    if (params.slug === 'statuses') {
        return getStatusList(fetch, url);
    }

    return getBookList(fetch, params);
};

async function getLikesList(
    fetch: ((input: URL | RequestInfo, init?: RequestInit | undefined) => Promise<Response>) | undefined,
): Promise<BookList> {
    const likesApiClient = new LikesApiClient(fetch);
    const response = await likesApiClient.index();

    if (!response.ok) {
        throw error(StatusCodes.clientError.notFound, 'Not found');
    }

    return {
        id: '',
        name: t.get('lists.liked'),
        // eslint-disable-next-line camelcase
        created_at: '',
        // eslint-disable-next-line camelcase
        updated_at: '',
        books: response.data,
    };
}

async function getStatusList(
    fetch: ((input: URL | RequestInfo, init?: RequestInit | undefined) => Promise<Response>) | undefined,
    url: URL,
): Promise<BookList> {
    const type = url.searchParams.get('type');
    if (type === null || !isBookReadStatusType(type)) {
        throw error(StatusCodes.clientError.notFound, 'Not found');
    }

    const bookReadStatusesApiClient = new BookReadStatusesApiClient(fetch);
    const response = await bookReadStatusesApiClient.show(type);

    return {
        id: '',
        name: t.get(`bookStatuses.${type}`),
        // eslint-disable-next-line camelcase
        created_at: '',
        // eslint-disable-next-line camelcase
        updated_at: '',
        books: response.data,
    };
}

async function getBookList(
    fetch: ((input: URL | RequestInfo, init?: RequestInit | undefined) => Promise<Response>) | undefined,
    params: {slug: string},
): Promise<BookList> {
    const bookListsApiClient = new BookListsApiClient(fetch);
    const response = await bookListsApiClient.show(params.slug);

    if (!response.ok) {
        throw error(StatusCodes.clientError.notFound, 'Not found');
    }

    return response.data;
}
