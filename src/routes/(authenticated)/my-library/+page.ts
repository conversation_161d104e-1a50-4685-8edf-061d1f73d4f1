import {error} from '@sveltejs/kit';
import BookListsApiClient from '$lib/api/BookListsApiClient';
import StatusCodes from '$routes/StatusCodes';
import type {PageLoad} from './$types';

export const load: PageLoad = async function ({fetch}) {
    const bookListsApiClient = new BookListsApiClient(fetch);
    const response = await bookListsApiClient.index();

    if (!response.ok) {
        throw error(StatusCodes.clientError.notFound, 'Not found');
    }

    return response.data;
};
