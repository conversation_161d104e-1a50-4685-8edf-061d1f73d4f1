<?php declare(strict_types=1);

namespace App\Console\Commands\Recommendations;

use App\Helpers\Arr;
use App\Models\Book;
use App\Models\Recommendation;
use App\Models\RecommendationType;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;

final class GenerateBasicRecommendationsForUserCommand extends Command
{
    protected $signature = 'recommendations:user-basic {user : User ID}';
    protected $description = 'Generate book basic recommendations for a specific user.';

    public function handle(): int
    {
        $userId = $this->argument('user');
        $user = User::find($userId);

        $this->generateLikeRecommendations($user);
        $this->generateLikeFreeRecommendations($user);
        $this->generateAuthorBookRecommendations($user);

        $this->info("Book recommendations for user {$userId} have been successfully generated.");

        return Command::SUCCESS;
    }

    private function generateLikeRecommendations(User $user): void
    {
        $recommendationScores = Config::get('recommendations.scores.likes');
        $recommendations = [];

        foreach ($user->bookLikes as $bookLike) {
            foreach ($bookLike->book->similarBooks as $similarBook) {
                $recommendations = $this->addBookToRecommendationsList($similarBook, $recommendationScores['book_like'], $recommendations);
            }
        }

        foreach ($user->bookShares as $bookShare) {
            foreach ($bookShare->book->similarBooks as $similarBook) {
                $recommendations = $this->addScoreToRecommendationsList($similarBook, $recommendationScores['book_share'], $recommendations);
            }
        }

        foreach ($user->authorsFollowed as $authorFollowed) {
            foreach ($authorFollowed->books as $authorBook) {
                $recommendations = $this->addScoreToRecommendationsList($authorBook, $recommendationScores['author_follow'], $recommendations);
            }
        }

        $this->saveRecommendations($recommendations, 'likes', $user);
    }

    private function generateLikeFreeRecommendations(User $user): void
    {
        $recommendationScores = Config::get('recommendations.scores.likes_free');
        $recommendations = [];

        foreach ($user->bookLikes as $bookLike) {
            foreach ($bookLike->book->similarFreeBooks as $similarBook) {
                $recommendations = $this->addBookToRecommendationsList($similarBook, $recommendationScores['book_like'], $recommendations);
            }
        }

        foreach ($user->bookShares as $bookShare) {
            foreach ($bookShare->book->similarFreeBooks as $similarBook) {
                $recommendations = $this->addScoreToRecommendationsList($similarBook, $recommendationScores['book_share'], $recommendations);
            }
        }

        foreach ($user->authorsFollowed as $authorFollowed) {
            foreach ($authorFollowed->books()->whereHas('editions', function ($query): void {
                $query->whereNotNull('google_id');
            })->get() as $authorBook) {
                $recommendations = $this->addScoreToRecommendationsList($authorBook, $recommendationScores['author_follow'], $recommendations);
            }
        }

        $this->saveRecommendations($recommendations, 'likes_free', $user);
    }

    private function generateAuthorBookRecommendations(User $user): void
    {
        $recommendationScores = Config::get('recommendations.scores.authors');
        $recommendations = [];

        foreach ($user->authorsFollowed as $authorFollowed) {
            foreach ($authorFollowed->books as $authorBook) {
                $recommendations = $this->addBookToRecommendationsList($authorBook, $recommendationScores['author_follow'], $recommendations);
            }
        }

        foreach ($user->bookLikes as $bookLike) {
            foreach ($bookLike->book->similarBooks as $similarBook) {
                $recommendations = $this->addScoreToRecommendationsList($similarBook, $recommendationScores['book_like'], $recommendations);
            }
        }

        foreach ($user->bookShares as $bookShare) {
            foreach ($bookShare->book->similarBooks as $similarBook) {
                $recommendations = $this->addScoreToRecommendationsList($similarBook, $recommendationScores['book_share'], $recommendations);
            }
        }

        $this->saveRecommendations($recommendations, 'authors', $user);
    }

    private function addBookToRecommendationsList(Book $book, int $score, array $recommendations): array
    {
        if ($book->isDisliked()) {
            return $recommendations;
        }

        if (empty($recommendations[$book->id])) {
            $recommendations[$book->id] = 0;
        }

        $recommendations[$book->id] += $score;

        return $recommendations;
    }

    private function addScoreToRecommendationsList(Book $book, int $score, array $recommendations): array
    {
        if ($book->isDisliked() || empty($recommendations[$book->id])) {
            return $recommendations;
        }

        $recommendations[$book->id] += $score;

        return $recommendations;
    }

    private function saveRecommendations(array $books, string $type, User $user): void
    {
        if (empty($books)) {
            return;
        }

        $recommendationType = RecommendationType::where('name', $type)->firstOrFail();
        $recommendationType->recommendations()->where('user_id', $user->id)->delete();

        arsort($books);

        $highestScore = $books[array_key_first($books)];
        $bookCountWithHighestScore = array_count_values($books)[$highestScore];
        $recommendationsCount = Config::get('recommendations.count_per_type');

        if ($bookCountWithHighestScore > $recommendationsCount) {
            $books = array_slice($books, 0, $bookCountWithHighestScore, true);
            $books = Arr::shuffleMaintainingIndexes($books);
        }

        $books = array_slice($books, 0, $recommendationsCount, true);
        foreach ($books as $bookId => $score) {
            $recommendation = new Recommendation();
            $recommendation->user_id = $user->id;
            $recommendation->book_id = $bookId;
            $recommendation->score = $score;
            $recommendationType->recommendations()->save($recommendation);
        }
    }
}
