<?php declare(strict_types=1);

namespace App\Console\Commands\Recommendations;

use App\Models\Book;
use App\Services\SimilarBooksService;
use Illuminate\Console\Command;

final class GenerateSimilarBooksCommand extends Command
{
    protected $signature = 'recommendations:similar';
    protected $description = 'Generate similar books.';
    public SimilarBooksService $similarBooksService;

    public function __construct(SimilarBooksService $similarBooksService)
    {
        parent::__construct();
        $this->similarBooksService = $similarBooksService;
    }

    public function handle(): int
    {
        $count = 0;
        Book::doesntHaveIn('similarBooks')
            ->select('id')
            ->chunk(1000, function ($books) use (&$count): void {
                foreach ($books as $book) {
                    $this->similarBooksService->getAndSaveSimilarBooks($book, false);
                    $this->similarBooksService->getAndSaveSimilarFreeBooks($book, false);
                }

                $count += 1000;
                $this->info("Similar books have been generated for {$count} books.");
            });

        $this->info('All similar books have been successfully generated.');

        return Command::SUCCESS;
    }
}
