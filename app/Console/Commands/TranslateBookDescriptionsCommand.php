<?php declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\Edition;
use App\Models\EditionDescription;
use App\Models\Language;
use App\Services\SanitizerService;
use App\Services\TranslatorService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;

final class TranslateBookDescriptionsCommand extends Command
{
    protected $signature = 'app:translate-book-descriptions';
    protected $description = 'Translate book descriptions.';
    private TranslatorService $translatorService;
    private SanitizerService $sanitizerService;

    public function __construct(TranslatorService $translatorService)
    {
        parent::__construct();

        $this->translatorService = $translatorService;
        $this->sanitizerService = new SanitizerService();
    }

    public function handle(TranslatorService $translator): void
    {
        $editions = Edition::whereNotNull('description')
            ->where('description', '!=', '')
            ->where(function ($query): void {
                $query->where('google_ratings_count', '>', 0)
                    ->orWhere('open_library_ratings_count', '>', 0)
                    ->orWhere('open_library_reading_logs_count', '>', 0);
            })
            ->get();

        $userLanguages = Config::get('settings.user_languages');
        $languages = [];
        foreach ($userLanguages as $userLanguage) {
            $language = Language::whereCode($userLanguage)->firstOrFail();
            $languages[$language->id] = $userLanguage;
        }

        $translationService = Config::get('settings.translation_service');

        foreach ($editions as $edition) {
            $description = $this->sanitizerService->sanitizeDescription($edition->description);
            foreach ($languages as $languageId => $languageCode) {
                $translatedDescription = $this->translatorService->translate($description, $languageCode);
                if ($translatedDescription === null) {
                    continue;
                }

                $editionDescription = new EditionDescription();
                $editionDescription->text = $translatedDescription;
                $editionDescription->edition_id = $edition->id;
                $editionDescription->language_id = $languageId;
                $editionDescription->translation_service = $translationService;
                $editionDescription->save();
            }
        }
    }
}
