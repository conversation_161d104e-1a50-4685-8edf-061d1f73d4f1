<?php declare(strict_types=1);

namespace App\Console\Commands\Imports\Audiobooks;

use App\Models\Audiobook;
use App\Models\AudiobookChapter;
use App\Models\AudiobookSubject;
use App\Models\Book;
use App\Models\Language;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

final class ImportAudiobooksCommand extends Command
{
    public const INTERNET_ARCHIVE_URL = 'https://archive.org';
    public const INTERNET_ARCHIVE_SEARCH_URL = self::INTERNET_ARCHIVE_URL . '/services/search/v1/scrape?q=collection:librivoxaudio'
        . '&fields=avg_rating,creator,downloads,format,identifier,language,num_reviews,publicdate,subject,title'
        . '&sorts=publicdate+asc&count=100';
    public const INTERNET_ARCHIVE_DETAILS_URL = self::INTERNET_ARCHIVE_URL . '/details/';

    protected $signature = 'import:audiobooks';
    protected $description = 'Import librivox audiobooks from archive.org.';

    public function handle(): int
    {
        $this->info('Importing audiobooks...');

        $counter = 0;
        $audiobooks = null;

        do {
            $url = self::INTERNET_ARCHIVE_SEARCH_URL;
            if (!empty($audiobooks)) {
                $url .= '&cursor=' . $audiobooks['cursor'];
            }

            $this->output->write('Get start ');
            $this->output->write($url . ' ');
            $audiobooks = Http::timeout(60)
                ->retry(10, 2000, function (Exception $exception): bool {
                    $this->error($exception->getMessage());

                    return true;
                })
                ->get($url)
                ->json();
            $this->output->write('Get end ');

            foreach ($audiobooks['items'] as $audiobook) {
                $isSaved = $this->saveAudiobook($audiobook);
                if ($isSaved) {
                    $counter++;

                    if ($counter % 100 === 0) {
                        $this->newLine();
                        $this->info("{$counter} audiobooks were imported.");
                    }
                }
            }
        } while (!empty($audiobooks['cursor']));

        $this->newLine();
        $this->info("The import for audiobooks was successful! {$counter} audiobooks were imported.");

        return Command::SUCCESS;
    }

    private function saveAudiobook(array $audiobook): bool
    {
        $titleLower = Str::lower($audiobook['title']);
        if (Str::contains($titleLower, 'librivox')) {
            return false;
        }

        $audiobookModel = Audiobook::where('archive_identifier', $audiobook['identifier'])->first();
        if (!empty($audiobookModel)) {
            return false;
        }

        $audiobookChapters = $this->getAudiobookChapters($audiobook['identifier'], $audiobook['format'], $audiobook['title']);
        if (empty($audiobookChapters)) {
            return false;
        }

        if (!empty($audiobook['creator']) && is_array($audiobook['creator'])) {
            $audiobook['creator'] = implode(', ', $audiobook['creator']);
        }

        $audiobookModel = new Audiobook();
        $audiobookModel->uuid = Str::uuid()->toString();
        $audiobookModel->title = $audiobook['title'];
        $audiobookModel->creator = $audiobook['creator'] ?? null;
        $audiobookModel->archive_identifier = $audiobook['identifier'];
        $audiobookModel->archive_downloads_count = $audiobook['downloads'] ?? 0;
        $audiobookModel->archive_average_rating = $audiobook['avg_rating'] ?? null;
        $audiobookModel->archive_reviews_count = $audiobook['num_reviews'] ?? 0;
        $audiobookModel->publication_date = $audiobook['publicdate'];

        if (!empty($audiobook['language']) && gettype($audiobook['language']) === 'string') {
            $language = $audiobook['language'];
            if (!empty(Language::LANGUAGE_CODE_MAP[$language])) {
                $language = Language::LANGUAGE_CODE_MAP[$language];
            }

            $languageModel = Language::where('name', $language)->orWhere('code', $language)->first();
            if (empty($languageModel)) {
                $languageModel = new Language();
                $languageModel->name = $language;
                $languageModel->code = $language;
                $languageModel->is_imported = true;
                $languageModel->save();
            }
            $audiobookModel->language_id = $languageModel->id;
        }

        $audiobookModel->save();

        foreach ((array)($audiobook['subject'] ?? []) as $subject) {
            if (str_contains(mb_strtolower($subject), 'librivox') || str_contains(mb_strtolower($subject), 'audiobook')) {
                continue;
            }

            $subjectModel = AudiobookSubject::whereRaw('LOWER(name) = ?', mb_strtolower($subject))->first();
            if (empty($subjectModel)) {
                $subjectModel = new AudiobookSubject();
                $subjectModel->name = ucfirst($subject);
                $subjectModel->save();
            }
            $audiobookModel->subjects()->syncWithoutDetaching($subjectModel);
        }

        foreach ($audiobookChapters as $chapter) {
            $chapterModel = new AudiobookChapter();
            $chapterModel->title = $chapter['name'];
            $chapterModel->url = $chapter['url'];
            $chapterModel->audiobook_id = $audiobookModel->id;
            $chapterModel->save();
        }

        if (empty($audiobook['creator']) || gettype($audiobook['creator']) !== 'string') {
            return true;
        }

        $bookId = Book::getId($audiobook['title'], $audiobook['creator']);
        if (!empty($bookId)) {
            $audiobookModel->book_id = $bookId;
            $audiobookModel->save();
        }

        return true;
    }

    private function getAudiobookChapters(string $identifier, array $formats, string $title): array
    {
        $fileQuality = null;
        if (in_array('128Kbps MP3', $formats)) {
            $fileQuality = '128';
        }
        if (empty($fileQuality) && in_array('64Kbps MP3', $formats)) {
            $fileQuality = '64';
        }
        if (empty($fileQuality)) {
            return [];
        }

        $urlRegex = "<a\\s[^>]*href=(\"??)([^\" >]*?{$fileQuality}kb\\.mp3)\\1[^>]*>(.*)<\\/a>"; // search for links that end in [128/64]kb.mp3
        $url = self::INTERNET_ARCHIVE_DETAILS_URL . $identifier;

        $this->output->write('Get ch start ');
        $this->output->write($url . ' ');

        try {
            $audiobookPage = Http::timeout(60)
                ->retry(10, 2000, function (Exception $exception): bool {
                    $this->error($exception->getMessage());

                    return $exception->getCode() !== 404;
                })
                ->get($url)
                ->body();
        } catch (RequestException $exception) {
            if ($exception->getCode() === 404) {
                return [];
            }

            throw $exception;
        }

        $this->output->write('Get end ');

        if (!preg_match_all("/{$urlRegex}/siU", $audiobookPage, $matches, PREG_SET_ORDER)) {
            $fileQuality = $fileQuality === '128' ? '64' : '128';
            $urlRegex = "<a\\s[^>]*href=(\"??)([^\" >]*?(?<!{$fileQuality}kb)\\.mp3)\\1[^>]*>(.*)<\\/a>"; // search for links that end in .mp3 excluding [64/128]kb.mp3 (sometimes the quality is missing from the links)

            if (!preg_match_all("/{$urlRegex}/siU", $audiobookPage, $matches, PREG_SET_ORDER)) {
                return [];
            }
        }

        $audiobookChapters = [];
        foreach ($matches as $match) {
            $name = strip_tags($match[3]);
            $name = str_replace('download', '', $name);
            $name = trim($name);

            if ($name === '1 file') {
                continue;
            }

            if ($name === '64KBPS MP3' || $name === '128KBPS MP3') {
                $name = $title;
            }

            $audiobookChapters[] = [
                'name' => $name,
                'url' => self::INTERNET_ARCHIVE_URL . $match[2],
            ];
        }

        return $audiobookChapters;
    }
}
