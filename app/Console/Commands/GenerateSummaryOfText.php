<?php declare(strict_types=1);

namespace App\Console\Commands;

use App\Services\SummaryGeneratorService;
use Illuminate\Console\Command;
use Exception;

final class GenerateSummaryOfText extends Command
{
    protected $signature = 'summary:text';
    protected $description = 'Generate summaries of texts in storage.';
    private SummaryGeneratorService $summaryGeneratorService;

    public function __construct(SummaryGeneratorService $summaryGeneratorService)
    {
        parent::__construct();
        $this->summaryGeneratorService = $summaryGeneratorService;
    }

    public function handle(): int
    {
        $directory = storage_path('app/texts');
        $files = scandir($directory);
        foreach ($files as $file) {
            if (!str_ends_with($file, '.txt')) {
                continue;
            }

            $isbn13 = str_replace('.txt', '', $file);
            $text = file_get_contents($directory . '/' . $file);

            try {
                $this->summaryGeneratorService->generateTextSummaries($isbn13, $text);
            } catch (Exception $e) {
                $this->error($e->getMessage() . ' - ' . $e->getFile() . ':' . $e->getLine());
                continue;
            }
        }

        return Command::SUCCESS;
    }
}
