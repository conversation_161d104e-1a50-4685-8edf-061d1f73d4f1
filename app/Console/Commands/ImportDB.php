<?php declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\Author;
use App\Models\Book;
use App\Models\Edition;
use App\Models\Language;
use App\Models\Subject;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

final class ImportDB extends Command
{
    public const CHUNK_SIZE = 10000;
    public const CHUNK_SIZE_BOOKS = 2000;

    protected $signature = 'import:db';
    protected $description = 'Import data from the database populated by the import applications.';

    public function handle(): int
    {
        $importConnection = 'mysql_import';

        Subject::disableSearchSyncing();
        Author::disableSearchSyncing();
        Book::disableSearchSyncing();
        DB::statement('SET FOREIGN_KEY_CHECKS = 0');

        $this->info('Importing authors...');
        DB::connection($importConnection)
            ->table('authors')
            ->orderBy('id')
            ->chunk(self::CHUNK_SIZE, function (Collection $authors): void {
                $this->importAuthors($authors);
            });

        $this->info('Importing publishers...');
        DB::connection($importConnection)
            ->table('publishers')
            ->orderBy('id')
            ->chunk(self::CHUNK_SIZE, function (Collection $publishers): void {
                $this->importPublishers($publishers);
            });

        $this->info('Importing subjects...');
        DB::connection($importConnection)
            ->table('subjects')
            ->orderBy('id')
            ->chunk(self::CHUNK_SIZE, function (Collection $subjects): void {
                $this->importSubjects($subjects);
            });

        $this->info('Importing books...');
        $languages = Language::all();
        $i = 0;

        $olRatings = DB::connection($importConnection)
            ->table('open_library_ratings')
            ->select('book_id', DB::raw('AVG(rating) AS open_library_rating'), DB::raw('COUNT(*) AS open_library_ratings_count'))
            ->groupBy('book_id');

        $olLogs = DB::connection($importConnection)
            ->table('open_library_reading_logs')
            ->select('book_id', DB::raw('COUNT(*) AS open_library_reading_logs_count'))
            ->groupBy('book_id');

        DB::connection($importConnection)
            ->table('books')
            ->leftJoin('languages', 'books.language_id', '=', 'languages.id')
            ->leftJoinSub($olRatings, 'ratings', function ($join): void {
                $join->on('books.id', '=', 'ratings.book_id');
            })
            ->leftJoinSub($olLogs, 'logs', function ($join): void {
                $join->on('books.id', '=', 'logs.book_id');
            })
            ->select([
                'books.*',
                'languages.name AS language_name',
                'ratings.open_library_rating',
                'ratings.open_library_ratings_count',
                'logs.open_library_reading_logs_count',
            ])
            ->orderBy('books.id')
            ->chunk(self::CHUNK_SIZE_BOOKS, function (Collection $books) use ($languages, &$i): void {
                $this->importBooks($books, $languages);

                $i += self::CHUNK_SIZE_BOOKS;
                $this->info("Imported {$i} books...");
            });

        $this->info('Importing industry identifiers...');
        DB::connection($importConnection)
            ->table('industry_identifiers')
            ->orderBy('id')
            ->chunk(self::CHUNK_SIZE, function (Collection $industryIdentifiers): void {
                $this->importIndustryIdentifiers($industryIdentifiers);
            });

        // manually copy the author_book and book_subject tables so it takes less time

        DB::statement('SET FOREIGN_KEY_CHECKS = 1');

        $this->info('Import finished.');

        return Command::SUCCESS;
    }

    private function importAuthors(Collection $authors): void
    {
        $now = Date::now();
        $queries = [];
        foreach ($authors as $author) {
            $queries[] = [
                'uuid' => Str::uuid()->toString(),
                'name' => $author->name,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        DB::table('authors')->insert($queries);
    }

    private function importPublishers(Collection $publishers): void
    {
        $now = Date::now();
        $queries = [];
        foreach ($publishers as $publisher) {
            $queries[] = [
                'name' => $publisher->name,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        DB::table('publishers')->insert($queries);
    }

    private function importSubjects(Collection $subjects): void
    {
        $now = Date::now();
        $queries = [];
        foreach ($subjects as $subject) {
            $queries[] = [
                'name' => $subject->name,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        DB::table('subjects')->insert($queries);
    }

    private function importBooks(Collection $books, Collection $languages): void
    {
        $now = Date::now();
        $bookQueries = [];
        foreach ($books as $book) {
            $bookQueries[] = [
                'uuid' => Str::uuid()->toString(),
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        DB::table('books')->insert($bookQueries);
        $firstId = DB::getPdo()->lastInsertId();

        $unknownLanguage = $languages->firstWhere('name', 'Unknown');

        $editionQueries = [];
        $editionIdentifierQueries = [];
        $i = 0;
        foreach ($books as $book) {
            $language = $this->getLanguage($book, $languages) ?? $unknownLanguage;
            $publishYear = $this->getPublishYear($book->date_published);
            $subtitle = $book->title_long ?: $book->subtitle;

            $bookId = $firstId + $i;
            $i++;
            $editionQueries[] = [
                'book_id' => $bookId,
                'title' => $book->title,
                'subtitle' => $subtitle !== $book->title ? $subtitle : null,
                'isbn_13' => $book->isbn13,
                'binding' => Str::limit($book->binding, 255, ''),
                'publisher_id' => $book->publisher_id,
                'language_id' => $language->id,
                'publish_date' => $book->date_published,
                'publish_year' => $publishYear,
                'edition' => Str::limit($book->edition, 255, ''),
                'number_of_pages' => $book->pages,
                'dimensions' => Str::limit($book->dimensions, 255, ''),
                'description' => $book->synopsis,
                'cover' => $book->image
                    ? Str::replace(Edition::ISBNDB_COVERS_BASE_URL . '/', '', $book->image)
                    : null,
                'msrp' => $book->msrp ?: null,
                'google_id' => $book->google_id,
                'google_rating' => $book->average_rating,
                'google_ratings_count' => $book->rating_count,
                'open_library_id' => $book->open_library_id,
                'open_library_rating' => $book->open_library_rating,
                'open_library_ratings_count' => $book->open_library_ratings_count ?? 0,
                'open_library_reading_logs_count' => $book->open_library_reading_logs_count ?? 0,
                'created_at' => $now,
                'updated_at' => $now,
            ];

            if ($book->isbn !== null && $book->isbn !== $book->isbn13) {
                $editionIdentifierQueries[] = [
                    'number' => $book->isbn,
                    'type' => 'ISBN_10',
                    'edition_id' => $bookId,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }
        }

        DB::table('editions')->insert($editionQueries);
        DB::table('edition_identifiers')->insert($editionIdentifierQueries);
    }

    private function importIndustryIdentifiers(Collection $industryIdentifiers): void
    {
        $now = Date::now();
        $queries = [];
        foreach ($industryIdentifiers as $industryIdentifier) {
            if ($industryIdentifier->type === 'ISBN_13') {
                continue;
            }

            if ($industryIdentifier->type === 'OTHER') {
                $identifier = explode(':', $industryIdentifier->identifier);
                $industryIdentifier->type = $identifier[0];
                $industryIdentifier->identifier = $identifier[1];
            }

            $queries[] = [
                'number' => $industryIdentifier->identifier,
                'type' => $industryIdentifier->type,
                'edition_id' => $industryIdentifier->book_id,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        DB::table('edition_identifiers')->insert($queries);
    }

    private function getLanguage(object $book, Collection $languages): ?Language
    {
        $splitTokens = [
            ';',
            ',',
            ' ',
            '-',
            '_',
        ];
        foreach ($splitTokens as $token) {
            if (Str::contains($book->language_name, $token)) {
                $book->language_name = Str::before($book->language_name, $token);
            }
        }

        return $languages->first(function ($language) use ($book) {
            $bookLanguage = Str::lower($book->language_name);

            return in_array($bookLanguage, [
                Str::lower($language->name),
                Str::lower($language->code),
                Str::lower($language->code_3_b),
                Str::lower($language->code_3_t),
            ]);
        });
    }

    private function getPublishYear(string $datePublished): ?string
    {
        $publishYear = null;
        $match = preg_match('/\d{4}/', $datePublished, $year);
        if (!empty($match) && !empty($year) && !empty($year[0])) {
            $publishYear = $year[0];
        }

        return $publishYear;
    }
}
