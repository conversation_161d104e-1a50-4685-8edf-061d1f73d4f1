<?php declare(strict_types=1);

namespace App\Services;

use Aws\Translate\TranslateClient as AwsTranslateClient;
use Exception;
use Google\Cloud\Translate\V3\Client\TranslationServiceClient as GoogleTranslateClient;
use Google\Cloud\Translate\V3\TranslateTextRequest;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Stichoza\GoogleTranslate\GoogleTranslate;

final class TranslatorService
{
    public const DEEPL_API_URL = 'https://api-free.deepl.com/v2/translate';
    public const AZURE_API_URL = 'https://api.cognitive.microsofttranslator.com';

    public function translate(string $text, string $language, string $service = null): ?string
    {
        if ($service === null) {
            $service = Config::get('settings.translation_service');
        }

        return match ($service) {
            'deepl' => $this->translateWithDeepl($text, $language),
            'aws' => $this->translateWithAws($text, $language),
            'azure' => $this->translateWithAzure($text, $language),
            'google' => $this->translateWithGoogle($text, $language),
            'google_free' => $this->translateWithFreeGoogle($text, $language),
        };
    }

    private function translateWithDeepl(string $text, string $language): ?string
    {
        $apiKey = Config::get('services.deepl.api_key');

        $response = Http::withHeaders([
            'Authorization' => "DeepL-Auth-Key {$apiKey}",
            'Content-Type' => 'application/json',
        ])->post(self::DEEPL_API_URL, [
            'text' => [$text],
            'target_lang' => $language,
            'context' => 'This is a book description.',
        ]);

        if ($response->failed()) {
            return null;
        }

        $data = $response->json();

        return $data['translations'][0]['text'] ?? null;
    }

    private function translateWithAws(string $text, string $language): ?string
    {
        $translateClient = new AwsTranslateClient([
            'credentials' => [
                'key' => Config::get('services.aws.key'),
                'secret' => Config::get('services.aws.secret'),
            ],
            'region' => Config::get('services.aws.region'),
            'version' => 'latest',
        ]);

        $result = $translateClient->translateText([
            'Text' => $text,
            'SourceLanguageCode' => 'en',
            'TargetLanguageCode' => $language,
        ]);

        return $result['TranslatedText'] ?? null;
    }

    private function translateWithAzure(string $text, string $language): ?string
    {
        $response = Http::withHeaders([
            'Ocp-Apim-Subscription-Key' => Config::get('services.azure.translation_api_key'),
            'Ocp-Apim-Subscription-Region' => Config::get('services.azure.region'),
            'Content-Type' => 'application/json',
            'X-ClientTraceId' => Str::uuid()->toString(),
        ])->post(self::AZURE_API_URL . "/translate?api-version=3.0&from=&to={$language}", [
            ['Text' => $text],
        ]);

        if ($response->failed()) {
            return null;
        }

        $data = $response->json();

        return $data[0]['translations'][0]['text'] ?? null;
    }

    private function translateWithGoogle(string $text, string $language): ?string
    {
        $projectId = Config::get('services.google.project_id');

        try {
            $translationClient = new GoogleTranslateClient([
                'credentials' => Config::get('services.google.application_credentials'),
            ]);
        } catch (Exception $e) {
            return null;
        }

        $location = $translationClient->locationName($projectId, 'global');

        $request = new TranslateTextRequest([
            'parent' => $location,
            'contents' => [$text],
            'mime_type' => 'text/plain',
            'target_language_code' => $language,
        ]);

        try {
            $response = $translationClient->translateText($request);
        } catch (Exception $e) {
            return null;
        }

        $translations = $response->getTranslations();

        $translationClient->close();

        return $translations[0]->getTranslatedText();
    }

    private function translateWithFreeGoogle(string $text, string $language): ?string
    {
        try {
            return GoogleTranslate::trans($text, $language);
        } catch (Exception $e) {
            return null;
        }
    }
}
