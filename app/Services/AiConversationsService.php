<?php declare(strict_types=1);

namespace App\Services;

use App\Enums\ResourceLimit;
use App\Http\Resources\AiConversationMessageResource;
use App\Models\AiConsumedResource;
use App\Models\AiConversation;
use App\Models\AiConversationMessage;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

final class AiConversationsService
{
    private const CACHE_KEY_PLATFORM_COST = 'ai-conversations.platform-cost-[date]';
    private const CACHE_KEY_USER_COST = 'ai-conversations.user-cost-[user_id]-[date]';
    private const LOCK_TIMEOUT_SECONDS = 60;
    private const LOCK_BLOCK_SECONDS = 10;
    private const BC_MATH_SCALE = 8;

    private BookLinker $bookLinker;

    public function __construct(BookLinker $bookLinker)
    {
        $this->bookLinker = $bookLinker;
    }

    /**
     * @throws Exception
     */
    public function sendMessage(AiConversation $aiConversation, string $newMessage): array
    {
        $resourceLimit = $this->checkIfResourceLimitReached();
        if ($resourceLimit !== ResourceLimit::NONE) {
            return [
                'reply' => '',
                'limitReached' => true,
                'messageLimitReached' => false,
            ];
        }

        $messageLimitReached = $this->checkIfMessageLimitReached($aiConversation);
        if ($messageLimitReached) {
            return [
                'reply' => '',
                'limitReached' => false,
                'messageLimitReached' => true,
            ];
        }

        $userMessage = new AiConversationMessage();
        $userMessage->content = $newMessage;
        $userMessage->is_sent_by_user = true;
        $userMessage->ai_conversation_id = $aiConversation->id;
        $userMessage->save();

        $input = $this->getAiInput($aiConversation, $newMessage);
        $aiResponse = $this->makeAiCall($input);

        $aiMessage = new AiConversationMessage();
        $aiMessage->content = $aiResponse['content'];
        $aiMessage->is_sent_by_user = false;
        $aiMessage->ai_conversation_id = $aiConversation->id;
        $aiMessage->save();

        $this->updateConsumedResources($aiResponse['usage']['input_tokens'], $aiResponse['usage']['output_tokens']);
        $resourceLimit = $this->checkIfResourceLimitReached();

        return [
            'reply' => $this->bookLinker->addLinks($aiResponse['content']),
            'limitReached' => $resourceLimit !== ResourceLimit::NONE,
            'messageLimitReached' => $this->checkIfMessageLimitReached($aiConversation),
        ];
    }

    private function checkIfResourceLimitReached(): ResourceLimit
    {
        $platformHardLimit = Config::get('ai_conversations.spend_limits_dollars_per_day.platform_hard');
        $platformSoftLimit = Config::get('ai_conversations.spend_limits_dollars_per_day.platform_soft');
        $userLimit = Config::get('ai_conversations.spend_limits_dollars_per_day.user');
        $userLimitIfPlatformSoftReached = Config::get('ai_conversations.spend_limits_dollars_per_day.user_if_platform_soft_reached');

        $platformCostToday = $this->calculatePlatformCostToday();
        if (bccomp((string)$platformCostToday, (string)$platformHardLimit, self::BC_MATH_SCALE) >= 0) {
            return ResourceLimit::PLATFORM;
        }

        $userCostToday = $this->calculateUserCostToday();
        $userAppliedLimit = bccomp((string)$platformCostToday, (string)$platformSoftLimit, self::BC_MATH_SCALE) >= 0 ? $userLimitIfPlatformSoftReached : $userLimit;
        if (bccomp((string)$userCostToday, (string)$userAppliedLimit, self::BC_MATH_SCALE) >= 0) {
            return ResourceLimit::USER;
        }

        return ResourceLimit::NONE;
    }

    private function checkIfMessageLimitReached(AiConversation $aiConversation): bool
    {
        $maxAiMessages = Config::get('ai_conversations.max_ai_messages_per_conversation');
        $aiMessageCount = $aiConversation->messages()->where('is_sent_by_user', false)->count();

        return $aiMessageCount >= $maxAiMessages;
    }

    private function getAiInput(AiConversation $aiConversation, string $newMessage): array
    {
        $input = [
            [
                'role' => 'system',
                'content' => Config::get('ai_conversations.system_message'),
            ],
        ];

        $messageHistory = AiConversationMessageResource::collection($aiConversation->messages)->toArray(new Request());
        foreach ($messageHistory as $message) {
            $input[] = [
                'role' => $message['isSentByUser'] ? 'user' : 'assistant',
                'content' => $message['content'],
            ];
        }

        $input[] = [
            'role' => 'user',
            'content' => $newMessage,
        ];

        return $input;
    }

    /**
     * @throws Exception
     */
    private function makeAiCall(array $input): array
    {
        $payload = [
            'model' => Config::get('ai_conversations.model'),
            'input' => $input,
            'text' => [
                'format' => [
                    'type' => 'text',
                ],
            ],
            'temperature' => Config::get('ai_conversations.temperature'),
            'max_output_tokens' => Config::get('ai_conversations.max_output_tokens'),
            'top_p' => Config::get('ai_conversations.top_p'),
            'store' => Config::get('ai_conversations.store'),
        ];

        if (Config::get('ai_conversations.use_web_search')) {
            $payload['tools'][] = [
                'type' => 'web_search_preview',
                'search_context_size' => 'high',
            ];
        }

        $openAiConfig = Config::get('services.openai');
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $openAiConfig['api_key'],
        ])->post($openAiConfig['api_url'], $payload);

        if ($response->failed()) {
            throw new Exception('Failed to send message to AI service: ' . $response->body());
        }

        $data = $response->json();
        $lastOutput = end($data['output']);
        $responseText = $lastOutput['content'][0]['text'];

        $usage = [
            'input_tokens' => $data['usage']['input_tokens'] ?? $this->estimateInputTokens($input),
            'output_tokens' => $data['usage']['output_tokens'] ?? $this->estimateOutputTokens($responseText),
        ];

        if (empty($data['usage']) || empty($data['usage']['input_tokens']) || empty($data['usage']['output_tokens'])) {
            Log::warning('No usage data found in API response', [
                'response' => $response->json(),
            ]);
        }

        return [
            'content' => $responseText,
            'usage' => $usage,
        ];
    }

    private function updateConsumedResources(int $inputTokens, int $outputTokens): void
    {
        $today = Date::today();
        $aiConsumedResource = Auth::user()->aiConsumedResources()->where('date', $today)->first();
        if ($aiConsumedResource === null) {
            $aiConsumedResource = new AiConsumedResource();
            $aiConsumedResource->input_tokens = 0;
            $aiConsumedResource->output_tokens = 0;
            $aiConsumedResource->user_id = Auth::id();
            $aiConsumedResource->date = $today;
        }

        $aiConsumedResource->input_tokens += $inputTokens;
        $aiConsumedResource->output_tokens += $outputTokens;
        $aiConsumedResource->save();

        $cost = $this->calculateCost($inputTokens, $outputTokens);

        $lockKeyPlatformCost = $this->getLockKeyPlatformCost();
        Cache::lock($lockKeyPlatformCost, self::LOCK_TIMEOUT_SECONDS)
            ->block(self::LOCK_BLOCK_SECONDS, function () use ($cost, $today): void {
                $cacheKeyPlatformCost = $this->getCacheKeyPlatformCost();
                if (!Cache::has($cacheKeyPlatformCost)) {
                    Log::warning('Platform cost not found in cache', [
                        'cacheKey' => $cacheKeyPlatformCost,
                    ]);
                }

                $currentPlatformCost = Cache::get($cacheKeyPlatformCost, '0');
                Cache::put(
                    $cacheKeyPlatformCost,
                    bcadd((string)$currentPlatformCost, (string)$cost, self::BC_MATH_SCALE),
                    $today->addDay(),
                );
            });

        $lockKeyUserCost = $this->getLockKeyUserCost();
        Cache::lock($lockKeyUserCost, self::LOCK_TIMEOUT_SECONDS)
            ->block(self::LOCK_BLOCK_SECONDS, function () use ($cost, $today): void {
                $cacheKeyUserCost = $this->getCacheKeyUserCost();
                if (!Cache::has($cacheKeyUserCost)) {
                    Log::warning('User cost not found in cache', [
                        'cacheKey' => $cacheKeyUserCost,
                    ]);
                }

                $currentUserCost = Cache::get($cacheKeyUserCost, '0');
                Cache::put(
                    $cacheKeyUserCost,
                    bcadd((string)$currentUserCost, (string)$cost, self::BC_MATH_SCALE),
                    $today->addDay(),
                );
            });
    }

    private function calculatePlatformCostToday(): float
    {
        $today = Date::today();
        $cacheKeyPlatformCost = $this->getCacheKeyPlatformCost();
        if (Cache::has($cacheKeyPlatformCost)) {
            return (float)Cache::get($cacheKeyPlatformCost);
        }

        $lockKeyPlatformCost = $this->getLockKeyPlatformCost();

        return Cache::lock($lockKeyPlatformCost, self::LOCK_TIMEOUT_SECONDS)
            ->block(self::LOCK_BLOCK_SECONDS, function () use ($cacheKeyPlatformCost, $today) {
                if (Cache::has($cacheKeyPlatformCost)) {
                    return (float)Cache::get($cacheKeyPlatformCost);
                }

                $aiConsumedResources = AiConsumedResource::whereDate('date', $today)->get();
                $platformCost = '0';
                foreach ($aiConsumedResources as $aiConsumedResource) {
                    $cost = $this->calculateCost($aiConsumedResource->input_tokens, $aiConsumedResource->output_tokens);
                    $platformCost = bcadd($platformCost, (string)$cost, self::BC_MATH_SCALE);
                }

                Cache::put($cacheKeyPlatformCost, $platformCost, $today->addDay());

                return (float)$platformCost;
            });
    }

    private function calculateUserCostToday(): float
    {
        $today = Date::today();
        $cacheKeyUserCost = $this->getCacheKeyUserCost();

        if (Cache::has($cacheKeyUserCost)) {
            return (float)Cache::get($cacheKeyUserCost);
        }

        $lockKeyUserCost = $this->getLockKeyUserCost();

        return Cache::lock($lockKeyUserCost, self::LOCK_TIMEOUT_SECONDS)
            ->block(self::LOCK_BLOCK_SECONDS, function () use ($cacheKeyUserCost, $today) {
                if (Cache::has($cacheKeyUserCost)) {
                    return (float)Cache::get($cacheKeyUserCost);
                }

                $userCost = 0;
                /** @var AiConsumedResource $aiConsumedResource */
                $aiConsumedResource = Auth::user()->aiConsumedResources()->whereDate('date', $today)->first();
                if ($aiConsumedResource !== null) {
                    $userCost = $this->calculateCost($aiConsumedResource->input_tokens, $aiConsumedResource->output_tokens);
                }

                Cache::put($cacheKeyUserCost, (string)$userCost, $today->addDay());

                return $userCost;
            });
    }

    private function calculateCost(int $inputTokens, int $outputTokens): float
    {
        $inputPricePerMillionTokens = Config::get('ai_conversations.pricing_dollars_per_1million_tokens.input');
        $outputPricePerMillionTokens = Config::get('ai_conversations.pricing_dollars_per_1million_tokens.output');

        // $inputTokens * $inputPricePerMillionTokens / 1000000
        $inputCost = bcdiv(
            bcmul((string)$inputTokens, (string)$inputPricePerMillionTokens, self::BC_MATH_SCALE),
            '1000000',
            self::BC_MATH_SCALE
        );

        // $outputTokens * $outputPricePerMillionTokens / 1000000
        $outputCost = bcdiv(
            bcmul((string)$outputTokens, (string)$outputPricePerMillionTokens, self::BC_MATH_SCALE),
            '1000000',
            self::BC_MATH_SCALE
        );

        return (float)bcadd($inputCost, $outputCost, self::BC_MATH_SCALE);
    }

    private function estimateInputTokens(array $input): int
    {
        $inputCharacters = 0;
        foreach ($input as $message) {
            $inputCharacters += Str::length($message['content']);
        }

        return (int)ceil($inputCharacters / 4);
    }

    private function estimateOutputTokens(string $output): int
    {
        $outputCharacters = Str::length($output);

        return (int)ceil($outputCharacters / 4);
    }

    private function getCacheKeyPlatformCost(): string
    {
        $today = Date::today()->format('Y-m-d');

        return Str::replace('[date]', $today, self::CACHE_KEY_PLATFORM_COST);
    }

    private function getCacheKeyUserCost(): string
    {
        $userId = Auth::id();
        $today = Date::today()->format('Y-m-d');

        return Str::replace(['[user_id]', '[date]'], [$userId, $today], self::CACHE_KEY_USER_COST);
    }

    private function getLockKeyPlatformCost(): string
    {
        return 'lock.' . $this->getCacheKeyPlatformCost();
    }

    private function getLockKeyUserCost(): string
    {
        return 'lock.' . $this->getCacheKeyUserCost();
    }
}
