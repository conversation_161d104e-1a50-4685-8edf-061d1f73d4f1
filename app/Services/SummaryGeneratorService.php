<?php declare(strict_types=1);

namespace App\Services;

use App\Jobs\GenerateTTSJob;
use App\Models\Book;
use App\Models\BookSummary;
use App\Models\Edition;
use App\Models\Language;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Exception;

final class SummaryGeneratorService
{
    private TranslatorService $translatorService;

    public function __construct(TranslatorService $translatorService, TTSService $ttsService)
    {
        $this->translatorService = $translatorService;
    }

    /**
     * @throws Exception
     */
    public function generateBookSummaries(Book $book): void
    {
        if ($book->summaries->count() > 0) {
            throw new Exception('Book already has summaries - ' . $book->uuid);
        }

        $edition = $book->getMainEdition();
        if ($edition === null) {
            throw new Exception('Book has no main edition - ' . $book->uuid);
        }

        $authorNames = $book->authors->pluck('name')->implode(', ');
        $summary = $this->generateOpenAiSummary("{$edition->title} by {$authorNames}");
        if ($summary === null) {
            throw new Exception('Failed to generate summary for ' . $edition->title);
        }

        $this->saveSummary($summary, 'en', $book);

        $userLanguages = Config::get('settings.user_languages');
        foreach ($userLanguages as $userLanguage) {
            if ($userLanguage === 'en') {
                continue;
            }

            $translatedSummary = $this->translatorService->translate($summary, $userLanguage);
            if ($translatedSummary === null) {
                throw new Exception('Failed to translate summary for ' . $edition->title);
            }

            $this->saveSummary($translatedSummary, $userLanguage, $book);
        }
    }

    /**
     * @throws Exception
     */
    public function generateTextSummaries(string $isbn13, string $text): void
    {
        $book = Edition::where('isbn_13', $isbn13)->firstOrFail()->book;
        if ($book->summaries->count() > 0) {
            throw new Exception('Book already has summaries - ' . $isbn13);
        }

        $summary = $this->generateOpenAiSummary($text);
        if ($summary === null) {
            throw new Exception('Failed to generate summary for ' . $isbn13);
        }

        $this->saveSummary($summary, 'en', $book);

        $userLanguages = Config::get('settings.user_languages');
        foreach ($userLanguages as $userLanguage) {
            if ($userLanguage === 'en') {
                continue;
            }

            $translatedSummary = $this->translatorService->translate($summary, $userLanguage);
            if ($translatedSummary === null) {
                continue;
            }

            $this->saveSummary($translatedSummary, $userLanguage, $book);
        }
    }

    private function generateOpenAiSummary(string $book): ?string
    {
        $payload = [
            'model' => 'gpt-4.1-mini',
            'input' => [
                [
                    'role' => 'system',
                    'content' => 'Summarize books in English, but make it rich, engaging, and detailed enough to take about 15 minutes to read.'
                        . 'Write in a natural, narrative tone — not as bullet points or as a list.'
                        . 'Present the book\'s ideas as a flowing story, weaving in its main concepts, themes, and discoveries.'
                        . 'Avoid outside opinions — stick closely to the content and spirit of the book.'
                        . 'Don\'t talk about the author or what the author does, just write the summary as if it was its own story.'
                        . 'Don\'t add advice, interpretation, or personal commentary — the summary should feel like a well-written distillation of the book itself, capturing the essence of what the author actually says.'
                        . 'Think of it like you\'re explaining the entire book clearly and compellingly to someone who won’t read it, but wants the full experience in one sitting.'
                        . 'It can be as long as it\'s necessary, just don\'t leave out any important details.'
                        . 'Do not list any sources in your response and do not use any headings.'
                        . 'Only reply with the summary and nothing else.'
                        . 'If you do not have information on that book respond with a "-" sign.',
                ],
                [
                    'role' => 'user',
                    'content' => "Here is the book to summarize: {$book}",
                ],
            ],
            'text' => [
                'format' => [
                    'type' => 'text',
                ],
            ],
            'tools' => [
                [
                    'type' => 'web_search_preview',
                    'search_context_size' => 'high',
                ],
            ],
            'temperature' => 0.3,
            'max_output_tokens' => 5000,
            'top_p' => 0.6,
            'store' => false,
        ];

        $apiKey = Config::get('services.openai.api_key');
        $apiUrl = 'https://api.openai.com/v1/responses';

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $apiKey,
        ])->post($apiUrl, $payload);

        if ($response->failed()) {
            return null;
        }

        $data = $response->json();
        $lastOutput = end($data['output']);
        $summary = $lastOutput['content'][0]['text'];

        return $summary === '-' ? null : $summary;
    }

    private function saveSummary(string $summary, string $languageCode, Book $book): void
    {
        $language = Language::whereCode($languageCode)->firstOrFail();

        $bookSummary = new BookSummary();
        $bookSummary->text = $summary;
        $bookSummary->book_id = $book->id;
        $bookSummary->language_id = $language->id;
        $bookSummary->translation_service = Config::get('settings.translation_service');
        $bookSummary->save();

        GenerateTTSJob::dispatch($book, $languageCode);
    }
}

/*
Provide book recommendations with accurate titles, descriptions, and reasons in a conversational style, focusing solely on book recommendations.

Respond only to book recommendation requests.
If necessary, ask follow-up questions to better understand user preferences.
Ensure book titles are official and accurately exist, avoiding incorrect translations.
Politely decline any non-book-related queries, emphasizing your focus.
Steps
Identify the Request: Check if the query is about book recommendations. If not, inform them that your expertise is limited to that area.
Clarify if Needed: Ask specific questions if the request is unclear.
Provide Recommendations: Offer books with official titles, engaging descriptions, and explanations in a conversational style.
Output Format
Recommendation Format:
"Book title" - author(s)
Include a brief, spoiler-free description.
Offer reasons in a conversational manner.
Examples
Example 1:

Input: "Give me a book about flowers."
Output:
"The Language of Flowers" - Vanessa Diffenbaugh
"This one's a gem! It dives into the Victorian language of flowers and tells the touching story of a young woman searching for her place in the world. It's a beautiful, heartfelt read that just fits perfectly if you're into floral themes."
Example 2:

Input: "I would like a list of books with an incredible plot twist."
Output:
"Gone Girl" - Gillian Flynn
"This thriller has a marriage at its core and goes off the rails in the best way possible. Trust me, the twists will knock your socks off!"
"The Girl with the Dragon Tattoo" - Stieg Larsson
"A gripping mystery with an investigative journalist and a brilliant hacker. It's full of surprises that'll keep you hooked."
Example 3:

Input: "What book resembles atomic habits?"
Output:
"The Power of Habit" - Charles Duhigg
"If you're into understanding the science behind habits, this is your book. It's packed with insights that are both practical and fascinating, quite similar to 'Atomic Habits'."
Example 4:

Input: "What's the weather today?"
Output: "I'm here just for book recommendations. Let me know if you'd like a suggestion!"

Notes
Ensure explanations are relatable and conversational.
Verify book titles for accuracy and authenticity, avoiding incorrect translations.
Maintain a friendly tone when addressing non-book requests.
 */
