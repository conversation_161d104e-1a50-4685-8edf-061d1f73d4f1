<?php declare(strict_types=1);

namespace App\Services;

use App\Models\Book;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

final class SimilarBooksService
{
    public const MAX_PAGE_NUMBER_DIFFERENCE = 20;
    public const MAX_PUBLISH_YEAR_DIFFERENCE = 20;
    public const BOOKS_COMPARE_LIMIT_LOW_QUALITY = 10;
    public const BOOKS_COMPARE_LIMIT_HIGH_QUALITY = 10000;

    /*
     * Increasing books compare limit will increase the quality of recommendations but will take longer to generate
     */
    public function getSimilarBooks(Book $book, bool $highQuality = true, bool $onlyFree = false): Collection
    {
        $sql = '
            WITH
                current_book_subjects AS (SELECT subjects.id FROM subjects INNER JOIN book_subject ON book_subject.subject_id = subjects.id WHERE book_subject.book_id = :bookId1),
                current_book_authors AS (SELECT authors.id as id FROM authors INNER JOIN author_book ON author_book.author_id = authors.id WHERE author_book.book_id = :bookId3)
            SELECT DISTINCT b.*, editions.number_of_pages,
            (
                SELECT COUNT(subjects.id)
                FROM subjects
                INNER JOIN book_subject ON book_subject.subject_id = subjects.id
                INNER JOIN current_book_subjects ON subjects.id = current_book_subjects.id
                WHERE book_subject.book_id = b.id
            ) as points_subjects,
            (
                SELECT COUNT(authors.id)
                FROM authors
                INNER JOIN author_book ON author_book.author_id = authors.id
                INNER JOIN current_book_authors ON authors.id = current_book_authors.id
                WHERE author_book.book_id = b.id
            ) as points_authors,
            CASE
                WHEN ABS(editions.number_of_pages - (SELECT number_of_pages FROM editions WHERE book_id = :bookId4 LIMIT 1)) + 1 BETWEEN 1 AND :maxPageNumberDifference + 1 THEN 1
                ELSE 0
            END as points_pages,
            CASE
                WHEN ABS(editions.publish_year - (SELECT editions.publish_year FROM editions WHERE book_id = :bookId5 LIMIT 1)) + 1 BETWEEN 1 AND :maxPublishYearDifference + 1 THEN 1
                ELSE 0
            END as points_date,
            (SELECT points_subjects) * :pointsSubject + (SELECT points_authors) * :pointsAuthor  + (SELECT points_pages) * :pointsNumberOfPages  + (SELECT points_date) * :pointsPublicationDate as points
            FROM books b
            INNER JOIN editions ON editions.book_id = b.id
            INNER JOIN book_subject ON book_subject.book_id = b.id
            INNER JOIN subjects ON subjects.id = book_subject.subject_id
            INNER JOIN current_book_subjects ON subjects.id = current_book_subjects.id
            WHERE b.id != :bookId6' . ($onlyFree ? ' AND editions.google_id IS NOT NULL' : '') . '
            LIMIT :booksSelectLimit;
        ';
        $query = DB::raw($sql)->getValue(DB::connection()->getQueryGrammar());
        $configPoints = Config::get('recommendations.scores.similar_books');
        $similarBooks = Book::fromQuery($query, [
            'bookId1' => $book->id,
            'bookId3' => $book->id,
            'bookId4' => $book->id,
            'bookId5' => $book->id,
            'bookId6' => $book->id,
            'maxPageNumberDifference' => self::MAX_PAGE_NUMBER_DIFFERENCE,
            'maxPublishYearDifference' => self::MAX_PUBLISH_YEAR_DIFFERENCE,
            'pointsSubject' => $configPoints['subject'],
            'pointsAuthor' => $configPoints['author'],
            'pointsNumberOfPages' => $configPoints['number_of_pages'],
            'pointsPublicationDate' => $configPoints['publication_date'],
            'booksSelectLimit' => $highQuality ? self::BOOKS_COMPARE_LIMIT_HIGH_QUALITY : self::BOOKS_COMPARE_LIMIT_LOW_QUALITY,
        ]);

        $similarBooks = $similarBooks->sortByDesc('points');

        return $similarBooks->unique('id')->slice(0, 4)->values();
    }

    public function getAndSaveSimilarBooks(Book $book, bool $highQuality = true): Collection
    {
        $similarBooks = $this->getSimilarBooks($book, $highQuality);

        $sync = [];
        foreach ($similarBooks as $similarBook) {
            $sync[$similarBook->id] = [
                'points' => $similarBook->points,
                'books_compare_limit' => $highQuality ? self::BOOKS_COMPARE_LIMIT_HIGH_QUALITY : self::BOOKS_COMPARE_LIMIT_LOW_QUALITY,
            ];
        }
        $book->similarBooks()->detach();
        $book->similarBooks()->attach($sync);

        return $similarBooks;
    }

    public function getAndSaveSimilarFreeBooks(Book $book, bool $highQuality = true): Collection
    {
        $similarBooks = $this->getSimilarBooks($book, $highQuality, true);

        $sync = [];
        foreach ($similarBooks as $similarBook) {
            $sync[$similarBook->id] = [
                'points' => $similarBook->points,
                'books_compare_limit' => $highQuality ? self::BOOKS_COMPARE_LIMIT_HIGH_QUALITY : self::BOOKS_COMPARE_LIMIT_LOW_QUALITY,
            ];
        }
        $book->similarFreeBooks()->detach();
        $book->similarFreeBooks()->attach($sync);

        return $similarBooks;
    }
}
