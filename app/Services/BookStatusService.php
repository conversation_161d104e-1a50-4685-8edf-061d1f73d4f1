<?php declare(strict_types=1);

namespace App\Services;

use App\Models\BookReadStatus;
use App\Models\BookReadStatusType;
use Illuminate\Support\Facades\Auth;

final class BookStatusService
{
    public function getBooksGroupedByStatus(bool $isLimited = true): array
    {
        $userBooksWithReadStatus = Auth::user()->booksWithReadStatus;

        $bookReadStatuses = [];
        $bookReadStatusTypes = BookReadStatusType::all();
        foreach ($bookReadStatusTypes as $bookReadStatusType) {
            $bookReadStatuses[$bookReadStatusType->name] = $userBooksWithReadStatus->filter(
                fn (BookReadStatus $bookReadStatus) => $bookReadStatus->book_read_status_type_id == $bookReadStatusType->id
            );

            if ($isLimited) {
                $bookReadStatuses[$bookReadStatusType->name] = $bookReadStatuses[$bookReadStatusType->name]->take(5);
            }

            $bookReadStatuses[$bookReadStatusType->name] = $bookReadStatuses[$bookReadStatusType->name]->map(
                fn (BookReadStatus $bookReadStatus) => $bookReadStatus->book->toDTO()
            )->values();
        }

        return $bookReadStatuses;
    }
}
