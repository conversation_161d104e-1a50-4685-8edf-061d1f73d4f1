<?php declare(strict_types=1);

namespace App\Services;

use App\Models\Book;

final class BookLinker
{
    public function addLinks(string $text): string
    {
        // Pattern to match book recommendations: "Book title" - author(s)
        $pattern = '/"([^"]+)"\s*-\s*([^"\n\r]+)/';

        return preg_replace_callback($pattern, function ($matches) {
            $title = trim($matches[1]);
            $authorsText = trim($matches[2]);

            $bookUuid = $this->findBookUuid($title, $authorsText);
            if ($bookUuid) {
                return '<a href="/books/' . $bookUuid . '">"' . $title . '" - ' . $authorsText . '</a>';
            }

            return $matches[0];
        }, $text);
    }

    private function findBookUuid(string $title, string $authorsText): ?string
    {
        $authors = array_map('trim', explode(',', $authorsText));

        foreach ($authors as $author) {
            $bookUuid = Book::getUuid($title, $author);

            if ($bookUuid !== null) {
                return $bookUuid;
            }
        }

        return null;
    }
}
