<?php declare(strict_types=1);

namespace App\Services;

use App\Models\Book;
use App\Models\Language;
use Exception;
use Illuminate\Support\Facades\Config;

final class TTSService
{
    private const AUDIO_FORMAT = 'mp3';

    /**
     * @throws Exception
     */
    public function generate(Book $book, string $languageCode): ?string
    {
        $language = Language::whereCode($languageCode)->firstOrFail();
        $summary = $book->summaries->where('language_id', $language->id)->firstOrFail()->text;
        $summary = str_replace('"', '\"', $summary);

        $pythonPath = Config::get('settings.python_path');
        if ($pythonPath === null || !file_exists($pythonPath)) {
            throw new Exception('Python path is not configured or does not exist.');
        }

        $ttsFilePath = Config::get('settings.tts_script_path');
        if ($ttsFilePath === null || !file_exists($ttsFilePath)) {
            throw new Exception('TTS script path is not configured or does not exist.');
        }

        $directory = $this->getDirectoryPath($book);
        if (!is_dir($directory)) {
            mkdir($directory, 0o755, true);
        }

        $filePath = $this->getFilePath($book, $languageCode);
        shell_exec("{$pythonPath} {$ttsFilePath} \"{$filePath}\" \"{$languageCode}\" \"{$summary}\"");

        return file_exists($filePath) ? $filePath : null;
    }

    public function getFilePath(Book $book, string $languageCode): string
    {
        return $this->getDirectoryPath($book) . '/' . $languageCode . '.' . self::AUDIO_FORMAT;
    }

    private function getDirectoryPath(Book $book): string
    {
        return storage_path("app/tts/{$book->uuid}");
    }
}
