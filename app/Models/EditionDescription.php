<?php declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\EditionDescription
 *
 * @property int $id
 * @property string $text
 * @property int $edition_id
 * @property int $language_id
 * @property string $translation_service
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class EditionDescription extends Model
{
    public function edition(): BelongsTo
    {
        return $this->belongsTo(Edition::class);
    }

    public function language(): BelongsTo
    {
        return $this->belongsTo(Language::class);
    }
}
