<?php declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\AiConversationMessages
 *
 * @property int $id
 * @property string $content
 * @property bool $is_sent_by_user
 * @property int $ai_conversation_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class AiConversationMessage extends Model
{
    protected $casts = [
        'is_sent_by_user' => 'boolean',
    ];

    public function aiConversation(): BelongsTo
    {
        return $this->belongsTo(AiConversation::class);
    }
}
