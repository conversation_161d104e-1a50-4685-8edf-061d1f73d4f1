<?php declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\Recommendation
 *
 * @property int $id
 * @property int|null $user_id
 * @property int $book_id
 * @property int $score
 * @property int $recommendation_type_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class Recommendation extends Model
{
    public function type(): BelongsTo
    {
        return $this->belongsTo(RecommendationType::class, 'recommendation_type_id');
    }

    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    public static function popular(): Collection
    {
        $type = RecommendationType::where('name', 'popular')->firstOrFail();

        return Recommendation::where('recommendation_type_id', $type->id)->get();
    }
}
