<?php declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Carbon;

/**
 * App\Models\AudiobookSubject
 *
 * @property int $id
 * @property string $name
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class AudiobookSubject extends Model
{
    public function audiobooks(): BelongsToMany
    {
        return $this->BelongsToMany(Audiobook::class);
    }
}
