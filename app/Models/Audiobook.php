<?php declare(strict_types=1);

namespace App\Models;

use App\Http\Resources\AudiobookResource;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;

/**
 * App\Models\Audiobook
 *
 * @property int $id
 * @property string $uuid
 * @property string $title
 * @property string|null $creator
 * @property string $archive_identifier
 * @property int $archive_downloads_count
 * @property int|null $archive_average_rating
 * @property int $archive_reviews_count
 * @property Carbon $publication_date
 * @property int|null $language_id
 * @property int|null $book_id
 * @property int $views_count
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class Audiobook extends Model
{
    protected $casts = [
        'publication_date' => 'datetime',
    ];

    public function subjects(): BelongsToMany
    {
        return $this->belongsToMany(AudiobookSubject::class);
    }

    public function chapters(): HasMany
    {
        return $this->hasMany(AudiobookChapter::class);
    }

    public function language(): BelongsTo
    {
        return $this->belongsTo(Language::class);
    }

    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    public static function search(array $parameters): array
    {
        [
            $title,
            $author,
            $subject,
            $yearFrom,
            $yearTo,
            $language,
            $offset,
            $limit,
        ] = $parameters;

        $audiobooks = Audiobook::with(['subjects', 'chapters'])
            ->select('audiobooks.*');

        if (!empty($title)) {
            $audiobooks = $audiobooks->whereRaw('MATCH(audiobooks.title) AGAINST(?)', ['"' . mb_strtolower($title) . '"']);
        }
        if (!empty($author)) {
            $audiobooks = $audiobooks->whereRaw('MATCH(audiobooks.creator) AGAINST(?)', ['"' . mb_strtolower($author) . '"']);
        }
        if (!empty($subject)) {
            $audiobooks = $audiobooks->join('audiobook_audiobook_subject', 'audiobooks.id', '=', 'audiobook_audiobook_subject.audiobook_id')
                ->join('audiobook_subjects', 'audiobook_subjects.id', '=', 'audiobook_audiobook_subject.audiobook_subject_id')
                ->where('audiobook_subjects.name', $subject);
        }
        if (!empty($yearFrom)) {
            $audiobooks = $audiobooks->whereYear('publication_date', '>=', (int)$yearFrom);
        }
        if (!empty($yearTo)) {
            $audiobooks = $audiobooks->whereYear('publication_date', '<=', (int)$yearTo);
        }
        if (!empty($language)) {
            $audiobooks = $audiobooks->join('languages', 'languages.id', '=', 'audiobooks.language_id')
                ->where('languages.code', $language);
        }

        $audiobooksCount = 0; // $books->count() is too slow (maybe different for audiobooks?)
        $audiobooks = $audiobooks->orderByDesc('archive_downloads_count')
            ->offset($offset)
            ->limit($limit)
            ->get();

        return [
            'totalResults' => $audiobooksCount,
            'data' => AudiobookResource::collection($audiobooks)->jsonSerialize(),
        ];
    }
}
