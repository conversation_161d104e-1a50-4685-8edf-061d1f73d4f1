<?php declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * App\Models\UserActivity
 *
 * @property int $id
 * @property int $user_id
 * @property Carbon $date
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class UserActivity extends Model
{
    protected $casts = [
        'date' => 'date',
    ];
}
