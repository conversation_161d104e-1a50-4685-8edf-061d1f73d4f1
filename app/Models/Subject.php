<?php declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Carbon;
use Laravel\Scout\Searchable;

/**
 * App\Models\Subject
 *
 * @property int $id
 * @property string $name
 * @property int $show_in_menu
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
final class Subject extends Model
{
    use Searchable;

    public function books(): BelongsToMany
    {
        return $this->belongsToMany(Book::class);
    }

    public function toSearchableArray(): array
    {
        return [
            'name' => $this->name,
        ];
    }

    public function getScoutKey(): int
    {
        return $this->id;
    }

    public function getScoutKeyName(): string
    {
        return 'id';
    }

    public function shouldBeSearchable(): bool
    {
        return (bool)$this->show_in_menu;
    }
}
