<?php declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * App\Models\AiConsumedResource
 *
 * @property int $id
 * @property int $input_tokens
 * @property int $output_tokens
 * @property int $user_id
 * @property Carbon $date
 */
final class AiConsumedResource extends Model
{
    public $timestamps = false;
    protected $casts = [
        'date' => 'date',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
