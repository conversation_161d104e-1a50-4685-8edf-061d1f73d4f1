<?php declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * App\Models\EditionIdentifier
 *
 * @property int $id
 * @property string $number
 * @property string $type
 * @property int $edition_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 */
final class EditionIdentifier extends Model
{
    use SoftDeletes;

    public const ISBN_13 = 'ISBN13';
    public const ISBN_10 = 'ISBN10';
    public const LCCN = 'LCCN';
    public const OCLC = 'OCLC';

    public function edition(): BelongsTo
    {
        return $this->belongsTo(Edition::class);
    }

    public function scopeDisplayed(Builder $query): Builder
    {
        return $query->whereIn('type', [
            self::ISBN_13,
            self::ISBN_10,
            self::OCLC,
            self::LCCN,
        ]);
    }
}
