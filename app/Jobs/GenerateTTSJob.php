<?php declare(strict_types=1);

namespace App\Jobs;

use App\Models\Book;
use App\Services\TTSService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Exception;

final class GenerateTTSJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public Book $book;
    public string $languageCode;

    public function __construct(Book $book, string $languageCode)
    {
        $this->book = $book->withoutRelations();
        $this->languageCode = $languageCode;
    }

    /**
     * @throws Exception
     */
    public function handle(TTSService $ttsService): void
    {
        $ttsService->generate($this->book, $this->languageCode);
    }
}
