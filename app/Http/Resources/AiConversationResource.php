<?php declare(strict_types=1);

namespace App\Http\Resources;

use App\Models\AiConversation;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin AiConversation */
final class AiConversationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'createdAt' => $this->created_at->diffForHumans(),
            'messages' => AiConversationMessageResource::collection($this->messages)->toArray($request),
        ];
    }
}
