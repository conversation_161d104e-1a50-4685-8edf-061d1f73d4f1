<?php declare(strict_types=1);

namespace App\Http\Resources;

use App\Models\ReadingActivity;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin ReadingActivity */
final class ReadingActivityResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->uuid,
            'startDate' => $this->start_date?->toDateString(),
            'endDate' => $this->end_date?->toDateString(),
            'pagesRead' => $this->pages_read,
            'totalPages' => $this->total_pages,
            'language' => $this->language ? new LanguageResource($this->language) : null,
        ];
    }
}
