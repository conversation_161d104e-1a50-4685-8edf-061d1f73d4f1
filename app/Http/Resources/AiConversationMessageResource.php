<?php declare(strict_types=1);

namespace App\Http\Resources;

use App\Models\AiConversationMessage;
use App\Services\BookLinker;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin AiConversationMessage */
final class AiConversationMessageResource extends JsonResource
{
    private BookLinker $bookLinker;

    public function __construct(AiConversationMessage $resource)
    {
        parent::__construct($resource);

        $this->bookLinker = new BookLinker();
    }

    public function toArray(Request $request): array
    {
        return [
            'content' => $this->bookLinker->addLinks($this->content),
            'isSentByUser' => $this->is_sent_by_user,
        ];
    }
}
