<?php declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Book;
use App\Models\BookReadStatus;
use App\Models\BookReadStatusType;
use App\Services\BookStatusService;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

final class BookReadStatusesController extends Controller
{
    private BookStatusService $bookStatusService;

    public function __construct()
    {
        $this->middleware([
            'auth:sanctum',
            'verified',
        ]);
        $this->bookStatusService = new BookStatusService();
    }

    public function index(Request $request): array
    {
        return $this->bookStatusService->index($request);
    }

    public function show(string $type): Collection
    {
        $bookReadStatusType = BookReadStatusType::where('name', $type)->firstOrFail();
        $booksWithReadStatus = Auth::user()
            ->booksWithReadStatus()
            ->where('book_read_status_type_id', $bookReadStatusType->id)
            ->get();

        return $booksWithReadStatus->map(
            fn (BookReadStatus $bookReadStatus) => $bookReadStatus->book->toDTO()
        )->values();
    }

    public function setBookStatus(Book $book, Request $request): array
    {
        $statusType = $request->get('statusType');
        $bookReadStatusType = BookReadStatusType::whereName($statusType)->first();
        if (empty($bookReadStatusType)) {
            return [
                'success' => false,
            ];
        }

        $authId = Auth::id();
        $bookReadStatus = new BookReadStatus();
        $bookReadStatus->book_read_status_type_id = $bookReadStatusType->id;
        $bookReadStatus->user_id = $authId;
        $book->readStatuses()->where('user_id', $authId)->delete();

        return [
            'success' => (bool)$book->readStatuses()->save($bookReadStatus),
        ];
    }

    public function removeBookStatus(Book $book): array
    {
        return [
            'success' => $book->readStatuses()->where('user_id', Auth::id())->delete() > 0,
        ];
    }
}
