<?php declare(strict_types=1);

namespace App\Http\Controllers;

use App\DataTransferObjects\BookDto;
use App\Models\Book;
use App\Models\BookList;
use App\Models\BookShare;
use App\Models\Language;
use App\Services\SimilarBooksService;
use App\Services\SummaryGeneratorService;
use App\Services\TTSService;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Illuminate\Support\Facades\Response;

final class BooksController extends Controller
{
    private SimilarBooksService $similarBooksService;
    private SummaryGeneratorService $summaryGeneratorService;
    private TTSService $ttsService;

    public function __construct(
        SimilarBooksService $similarBooksService,
        SummaryGeneratorService $summaryGeneratorService,
        TTSService $ttsService
    ) {
        $this->middleware([
            'auth:sanctum',
            'verified',
        ])->only([
            'addToList',
            'removeFromList',
            'summary',
        ]);

        $this->similarBooksService = $similarBooksService;
        $this->summaryGeneratorService = $summaryGeneratorService;
        $this->ttsService = $ttsService;
    }

    /*
     * This route is only used to get the default book data that is manually cached in the front-end.
     */
    public function index(Request $request): Collection
    {
        $configKey = 'settings.default_books.all';
        if (!empty($request->get('onlyFree'))) {
            $configKey = 'settings.default_books.free';
        }

        $bookUuids = Config::get($configKey);

        return collect($bookUuids)
            ->map(
                fn (string $uuid): BookDto => Book::whereUuid($uuid)
                    ->firstOrFail()
                    ->toDTO()
            );
    }

    public function show(Book $book): BookDto
    {
        $edition = $book->getMainEdition();
        if (empty($edition)) {
            abort(404);
        }

        Book::withoutSyncingToSearch(function () use ($edition): void {
            $edition->book->views_count++;
            $edition->book->save();
        });

        return $edition->toDTO();
    }

    public function similar(Book $book): Collection
    {
        if ($book->similarBooks->isEmpty()) {
            $similarBooks = $this->similarBooksService->getAndSaveSimilarBooks($book, false)->map->toDto();
            Artisan::queue('recommendations:similar-book', [
                'book' => $book->id,
            ]);

            return $similarBooks;
        }

        $similarBook = $book->similarBooks->first();
        if ($similarBook->pivot->books_compare_limit < SimilarBooksService::BOOKS_COMPARE_LIMIT_HIGH_QUALITY) {
            Artisan::queue('recommendations:similar-book', [
                'book' => $book->id,
            ]);
        }

        return $book->similarBooks->map->toDto();
    }

    public function setNotFree(Book $book): array
    {
        $success = $book->delete();

        return [
            'success' => $success,
        ];
    }

    public function addToList(Book $book, BookList $bookList): array
    {
        $bookList->books()->attach($book);

        return [
            'success' => true,
        ];
    }

    public function removeFromList(Book $book, BookList $bookList): array
    {
        $bookList->books()->detach($book);

        return [
            'success' => true,
        ];
    }

    public function share(Book $book, Request $request): array
    {
        $request->validate([
            'network' => 'required',
        ]);

        $bookShare = new BookShare();
        $bookShare->network = $request->get('network');
        $bookShare->user_id = Auth::id();
        $bookShare = $book->shares()->save($bookShare);

        Artisan::queue('recommendations:generate');

        return [
            'success' => $bookShare !== false,
        ];
    }

    public function summary(Book $book): array
    {
        if ($book->summaries->count() === 0) {
            $this->summaryGeneratorService->generateBookSummaries($book);
            $book->load('summaries');
        }

        $userLanguage = Language::where('code', Auth::user()->language)->firstOrFail();
        $summary = $book->summaries->where('language_id', $userLanguage->id)->firstOrFail()->text;
        $summary = Str::replace("\n", '<br>', $summary);

        return [
            'summary' => $summary,
        ];
    }

    public function summaryTTS(Book $book, string $languageCode): BinaryFileResponse
    {
        $ttsFilePath = $this->ttsService->getFilePath($book, $languageCode);
        if (!file_exists($ttsFilePath)) {
            $ttsFilePath = $this->ttsService->generate($book, $languageCode);
        }

        if ($ttsFilePath === null) {
            abort(404);
        }

        return Response::file($ttsFilePath);
    }
}
