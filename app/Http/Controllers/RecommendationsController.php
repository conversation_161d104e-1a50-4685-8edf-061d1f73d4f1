<?php declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Recommendation;
use App\Models\RecommendationType;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;

final class RecommendationsController extends Controller
{
    public function __construct()
    {
        $this->middleware([
            'auth:sanctum',
            'verified',
        ]);
    }

    public function index(): array
    {
        $recommendations = [];
        $recommendationTypes = RecommendationType::all();
        foreach ($recommendationTypes as $recommendationType) {
            $recommendations[$recommendationType->name] = [];
        }
        $recommendations['top'] = [];

        $user = Auth::user();
        if ($user->recommendations->isEmpty()) {
            return $recommendations;
        }

        $topCount = Config::get('recommendations.count_top');
        $recommendations['top'] = $user->recommendations->unique('book_id')->values()->sortByDesc('score');
        $highestScore = $recommendations['top'][0]->score;
        $scores = $recommendations['top']->pluck('score')->toArray();
        $topCountWithHighestScore = array_count_values($scores)[$highestScore];
        if ($topCountWithHighestScore > $topCount) {
            $recommendations['top'] = $recommendations['top']->slice(0, $topCountWithHighestScore)/* ->shuffle() */;
        }
        $recommendations['top'] = $recommendations['top']->take($topCount)->values()->map->book->map->toDto();

        $recommendations['popular'] = Recommendation::popular()->map->book->map->toDto();
        foreach ($user->recommendations as $recommendation) {
            $recommendations[$recommendation->type->name][] = $recommendation->book->toDto();
        }

        $recommendations['currentlyReading'] = $user->currentlyReadingBooks->map->book->map->toDto();

        return $recommendations;
    }
}
