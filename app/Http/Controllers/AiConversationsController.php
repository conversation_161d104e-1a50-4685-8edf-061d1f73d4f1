<?php declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Resources\AiConversationResource;
use App\Models\AiConversation;
use App\Services\AiConversationsService;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Exception;

final class AiConversationsController extends Controller
{
    private AiConversationsService $aiConversationsService;

    public function __construct(AiConversationsService $aiConversationsService)
    {
        $this->aiConversationsService = $aiConversationsService;
    }

    public function index(): array
    {
        $conversations = Auth::user()->aiConversations()->orderByDesc('created_at')->get();

        return AiConversationResource::collection($conversations)->jsonSerialize();
    }

    /**
     * @throws Exception
     */
    public function store(Request $request): array
    {
        $request->validate([
            'content' => 'required|string',
            'conversationId' => 'nullable|integer|exists:ai_conversations,id',
        ]);

        $content = $request->get('content');
        $conversationId = $request->get('conversationId');

        if ($conversationId !== null) {
            $aiConversation = AiConversation::with('messages')->findOrFail($conversationId);
        } else {
            $aiConversation = new AiConversation();
            $aiConversation->title = Str::limit($content, 255);
            $aiConversation->user_id = Auth::id();
            $aiConversation->save();
        }

        $response = $this->aiConversationsService->sendMessage($aiConversation, $content);

        return [
            'conversationId' => $aiConversation->id,
            'reply' => $response['reply'],
            'limitReached' => $response['limitReached'],
            'messageLimitReached' => $response['messageLimitReached'],
        ];
    }

    public function destroy(AiConversation $aiConversation)
    {
        if ($aiConversation->user_id !== Auth::id()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        return [
            'success' => $aiConversation->delete(),
        ];
    }
}
