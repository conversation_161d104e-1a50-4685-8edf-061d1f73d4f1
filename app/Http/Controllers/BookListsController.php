<?php declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Resources\AuthorResource;
use App\Http\Resources\BookListResource;
use App\Models\BookLike;
use App\Models\BookList;
use App\Services\BookStatusService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

final class BookListsController extends Controller
{
    private BookStatusService $bookStatusService;

    public function __construct(BookStatusService $bookStatusService)
    {
        $this->middleware([
            'auth:sanctum',
            'verified',
        ]);
        $this->bookStatusService = $bookStatusService;
    }

    public function index(): array
    {
        $bookLists = Auth::user()->bookLists;

        return [
            'lists' => BookListResource::collection($bookLists)->jsonSerialize(),
            'likes' => Auth::user()->bookLikes->take(5)->map(fn (BookLike $bookLike) => $bookLike->book->toDTO()),
            'followed' => AuthorResource::collection(Auth::user()->authorsFollowed)->jsonSerialize(),
            'statuses' => $this->bookStatusService->getBooksGroupedByStatus(),
        ];
    }

    public function store(Request $request): array
    {
        $request->validate([
            'name' => 'required|max:255',
        ]);

        $bookList = new BookList();
        $bookList->name = $request->get('name');
        $bookList->uuid = Str::uuid()->toString();

        $success = Auth::user()->bookLists()->save($bookList);

        return [
            'success' => $success !== false,
        ];
    }

    public function show(BookList $bookList, Request $request): array
    {
        $request->request->add(['allBooks' => true]);

        return (new BookListResource($bookList))->toArray($request);
    }

    public function update(BookList $bookList, Request $request): array
    {
        $request->validate([
            'name' => 'required|max:255',
        ]);

        $bookList->name = $request->get('name');
        $success = $bookList->save();

        return [
            'success' => $success,
        ];
    }

    public function destroy(BookList $bookList): array
    {
        $bookList->books()->detach();
        $success = $bookList->delete();

        return [
            'success' => $success,
        ];
    }
}
