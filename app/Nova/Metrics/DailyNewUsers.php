<?php declare(strict_types=1);

namespace App\Nova\Metrics;

use App\Models\User;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Metrics\Trend;
use <PERSON><PERSON>\Nova\Nova;
use DateInterval;
use DateTimeInterface;

final class DailyNewUsers extends Trend
{
    /**
     * Calculate the value of the metric.
     *
     */
    public function calculate(NovaRequest $request)
    {
        return $this->countByDays($request, User::class)
            ->showLatestValue();
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            30 => Nova::__('30 Days'),
            90 => Nova::__('90 Days'),
            365 => Nova::__('365 Days'),
        ];
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return DateTimeInterface|DateInterval|float|int|null
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'new-users-per-day';
    }
}
