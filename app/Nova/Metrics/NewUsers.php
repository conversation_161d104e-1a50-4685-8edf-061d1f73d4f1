<?php declare(strict_types=1);

namespace App\Nova\Metrics;

use App\Models\User;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Metrics\Value;
use <PERSON><PERSON>\Nova\Nova;
use DateInterval;
use DateTimeInterface;

final class NewUsers extends Value
{
    /**
     * Calculate the value of the metric.
     *
     */
    public function calculate(NovaRequest $request)
    {
        return $this->count($request, User::class);
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges()
    {
        return [
            30 => Nova::__('30 Days'),
            365 => Nova::__('365 Days'),
            'TODAY' => Nova::__('Today'),
            'YESTERDAY' => Nova::__('Yesterday'),
            'MTD' => Nova::__('Month To Date'),
            'YTD' => Nova::__('Year To Date'),
            'ALL' => 'All Time',
        ];
    }

    /**
     * Determine the amount of time the results of the metric should be cached.
     *
     * @return DateTimeInterface|DateInterval|float|int|null
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }
}
